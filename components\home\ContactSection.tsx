'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';

export default function ContactSection() {
  return (
    <div className="py-24 bg-gradient-to-b from-[#1a0b2e] via-black to-black relative overflow-hidden">
      {/* Background decoration - will be replaced later */}
      <div className="absolute inset-0 z-0 opacity-40">
        {/* Animated curved shapes will go here */}
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="text-center mb-16">
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
            className="text-3xl md:text-5xl font-bold mb-6 text-white"
          >
            START YOUR JOURNEY
          </motion.h2>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            viewport={{ once: true }}
            className="text-xl text-gray-300 max-w-3xl mx-auto"
          >
            Ready to transform your idea into a successful business? Apply to join our incubator program
          </motion.p>
        </div>

        <div className="max-w-2xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
            className="bg-black/50 backdrop-blur-sm border border-purple-500/20 rounded-xl p-8"
          >
            <form className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <label htmlFor="name" className="text-sm font-medium text-center block">
                    Name
                  </label>
                  <Input
                    id="name"
                    placeholder="Your full name"
                    className="bg-black/50 border-purple-500/20 focus:border-purple-500 text-center"
                  />
                </div>
                <div className="space-y-2">
                  <label htmlFor="email" className="text-sm font-medium text-center block">
                    Email
                  </label>
                  <Input
                    id="email"
                    type="email"
                    placeholder="Your email"
                    className="bg-black/50 border-purple-500/20 focus:border-purple-500 text-center"
                  />
                </div>
              </div>
              <div className="space-y-2">
                <label htmlFor="subject" className="text-sm font-medium text-center block">
                  Subject
                </label>
                <Input
                  id="subject"
                  placeholder="Business idea or inquiry"
                  className="bg-black/50 border-purple-500/20 focus:border-purple-500 text-center"
                />
              </div>
              <div className="space-y-2">
                <label htmlFor="message" className="text-sm font-medium text-center block">
                  Message
                </label>
                <Textarea
                  id="message"
                  placeholder="Tell us about your business idea and how we can help you succeed"
                  className="bg-black/50 border-purple-500/20 focus:border-purple-500 min-h-[120px] text-center"
                />
              </div>
              <div className="flex justify-center mt-8">
                <Button
                  size="lg"
                  className="rounded-full bg-gradient-to-r from-purple-700 to-indigo-800 hover:from-purple-600 hover:to-indigo-700 text-white group px-8 py-2 h-auto text-base font-medium"
                >
                  <span className="flex items-center">
                    Apply Now
                    <svg className="ml-2 h-4 w-4" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M3.33337 8H12.6667" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                      <path d="M8 3.33331L12.6667 7.99998L8 12.6666" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                  </span>
                </Button>
              </div>
            </form>
          </motion.div>

          <div className="flex justify-center mt-8">
            <motion.div
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.5 }}
              viewport={{ once: true }}
              className="flex flex-col md:flex-row items-center space-y-2 md:space-y-0 md:space-x-4 text-sm text-gray-400 text-center"
            >
              <span>CONTACT US</span>
              <span className="hidden md:inline">•</span>
              <span><EMAIL></span>
              <span className="hidden md:inline">•</span>
              <span>+976 (11) 77889900</span>
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  );
}
