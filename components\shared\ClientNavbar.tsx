'use client';

import dynamic from 'next/dynamic';
import { Suspense } from 'react';

// Import the Navbar component with SSR disabled
const Navbar = dynamic(() => import('./Navbar'), { ssr: false });

export default function ClientNavbar() {
  return (
    <Suspense fallback={<NavbarFallback />}>
      <Navbar />
    </Suspense>
  );
}

function NavbarFallback() {
  return (
    <header className="fixed top-0 left-0 right-0 z-50 bg-black/80 backdrop-blur-md border-b border-primary/10">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 flex items-center justify-between">
        <div className="flex items-center gap-2">
          <div className="relative w-10 h-10 bg-gradient-to-br from-primary to-primary/70 rounded-full flex items-center justify-center">
            <span className="text-primary-foreground font-bold text-xl">I</span>
          </div>
          <span className="font-bold text-xl">INNO<span className="text-primary">HUB</span></span>
        </div>
        
        {/* Placeholder for navigation */}
        <div className="hidden md:flex items-center gap-8">
          <div className="w-12 h-4 bg-gray-700/50 rounded"></div>
          <div className="w-12 h-4 bg-gray-700/50 rounded"></div>
          <div className="w-12 h-4 bg-gray-700/50 rounded"></div>
          <div className="w-12 h-4 bg-gray-700/50 rounded"></div>
        </div>
        
        {/* Placeholder for right side actions */}
        <div className="hidden md:flex items-center gap-4">
          <div className="w-24 h-8 bg-gray-700/50 rounded-full"></div>
          <div className="w-8 h-8 bg-gray-700/50 rounded-full"></div>
          <div className="w-24 h-8 bg-primary/30 rounded-full"></div>
        </div>
        
        {/* Mobile menu placeholder */}
        <div className="md:hidden">
          <div className="w-8 h-8 bg-gray-700/50 rounded"></div>
        </div>
      </div>
    </header>
  );
}
