'use client';

import { motion } from 'framer-motion';
import { useLanguage } from '@/lib/context/language-context';

interface GlowingInnoHubTextProps {
  className?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  showSubtitle?: boolean;
}

export const GlowingInnoHubText = ({ 
  className = '', 
  size = 'lg',
  showSubtitle = true 
}: GlowingInnoHubTextProps) => {
  const { language } = useLanguage();

  const sizeClasses = {
    sm: 'text-xl md:text-2xl',
    md: 'text-2xl md:text-3xl',
    lg: 'text-3xl md:text-4xl',
    xl: 'text-4xl md:text-5xl'
  };

  const paddingClasses = {
    sm: 'px-4 py-2',
    md: 'px-6 py-3',
    lg: 'px-8 py-4',
    xl: 'px-10 py-5'
  };

  return (
    <motion.div
      className={`flex justify-center ${className}`}
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: 0.6 }}
      viewport={{ once: true }}
    >
      <div className="relative">
        {/* Glow effect background */}
        <motion.div
          className="absolute inset-0 rounded-lg blur-xl opacity-50"
          animate={{
            background: [
              'linear-gradient(45deg, #a855f7, #6366f1)',
              'linear-gradient(45deg, #6366f1, #8b5cf6)',
              'linear-gradient(45deg, #8b5cf6, #a855f7)',
            ],
          }}
          transition={{
            duration: 3,
            repeat: Infinity,
            repeatType: 'reverse',
          }}
        />
        
        {/* Main text container */}
        <motion.div
          className={`relative ${paddingClasses[size]} bg-black/50 backdrop-blur-sm rounded-lg border border-primary/30`}
          whileHover={{ scale: 1.05 }}
          transition={{ duration: 0.3 }}
        >
          <h2 className={`${sizeClasses[size]} font-bold text-center`}>
            <span className="text-white">INNO</span>
            <motion.span
              className="text-primary"
              animate={{
                textShadow: [
                  '0 0 10px rgba(168, 85, 247, 0.5)',
                  '0 0 20px rgba(168, 85, 247, 0.8)',
                  '0 0 10px rgba(168, 85, 247, 0.5)',
                ],
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                repeatType: 'reverse',
              }}
            >
              HUB
            </motion.span>
          </h2>
          {showSubtitle && (
            <p className="text-center text-white/70 text-sm mt-2">
              {language === 'mn' ? 'Инновацийн төв' : 'Innovation Hub'}
            </p>
          )}
        </motion.div>
      </div>
    </motion.div>
  );
};
