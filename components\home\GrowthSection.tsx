'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { GrowthStages } from '@/components/ui/aceternity/growth-stages';

export default function GrowthSection() {
  return (
    <div className="py-24 bg-gradient-to-b from-black to-black/90">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
            className="text-3xl md:text-4xl font-bold mb-4"
          >
            From Idea to Profitable Business
          </motion.h2>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            viewport={{ once: true }}
            className="text-xl text-muted-foreground max-w-3xl mx-auto"
          >
            We support startups at every stage of growth with tailored resources and expertise
          </motion.p>
        </div>

        <GrowthStages className="mb-16" />

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-16">
          {[
            {
              id: 'ideation',
              title: 'Ideation & Validation',
              description: 'Transform your idea into a viable product through market research, customer validation, and MVP development',
              icon: '🌱',
            },
            {
              id: 'growth',
              title: 'Growth & Scaling',
              description: 'Access resources and expertise to rapidly grow your business, optimize operations, and expand your customer base',
              icon: '🌿',
            },
            {
              id: 'expansion',
              title: 'Market Expansion',
              description: 'Expand your business nationally and internationally with our strategic support and partnership network',
              icon: '🌳',
            },
          ].map((item) => (
            <motion.div
              key={item.id}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
              viewport={{ once: true }}
              className="bg-black/50 backdrop-blur-sm border border-primary/10 rounded-xl p-6"
            >
              <div className="text-4xl mb-4">{item.icon}</div>
              <h3 className="text-xl font-bold mb-2">{item.title}</h3>
              <p className="text-muted-foreground">{item.description}</p>
            </motion.div>
          ))}
        </div>
      </div>
    </div>
  );
}
