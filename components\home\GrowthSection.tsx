'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { GrowthStages } from '@/components/ui/aceternity/growth-stages';
import dynamic from 'next/dynamic';

// Dynamically import Spline to avoid SSR issues
const Spline = dynamic(() => import('@splinetool/react-spline/next'), {
  ssr: false,
  loading: () => (
    <div className="w-full h-full flex items-center justify-center">
      <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
    </div>
  ),
});

export default function GrowthSection() {
  return (
    <div className="relative min-h-screen bg-gradient-to-b from-black via-black/95 to-black/90 overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 bg-gradient-to-r from-purple-900/10 via-transparent to-purple-900/10" />
      <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_center,_var(--tw-gradient-stops))] from-purple-900/20 via-transparent to-transparent" />

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
        {/* Header Section */}
        <div className="text-center mb-20">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="inline-block"
          >
            <span className="inline-block px-4 py-2 rounded-full bg-gradient-to-r from-purple-500/20 to-purple-600/20 border border-purple-500/30 text-purple-300 text-sm font-medium mb-6">
              Innovation Journey
            </span>
          </motion.div>

          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.1 }}
            viewport={{ once: true }}
            className="text-4xl md:text-6xl lg:text-7xl font-bold mb-6 bg-gradient-to-r from-white via-purple-100 to-white bg-clip-text text-transparent leading-tight"
          >
            From Idea to
            <br />
            <span className="bg-gradient-to-r from-purple-400 to-purple-600 bg-clip-text text-transparent">
              Profitable Business
            </span>
          </motion.h2>

          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
            className="text-xl md:text-2xl text-gray-300 max-w-4xl mx-auto leading-relaxed"
          >
            We support startups at every stage of growth with tailored resources,
            expert mentorship, and cutting-edge technology to transform your vision into reality.
          </motion.p>
        </div>

        {/* Growth Stages */}
        <motion.div
          initial={{ opacity: 0, y: 40 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.3 }}
          viewport={{ once: true }}
        >
          <GrowthStages className="mb-20" />
        </motion.div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
          {/* Left Side - Feature Cards */}
          <div className="space-y-8">
            {[
              {
                id: 'ideation',
                title: 'Ideation & Validation',
                description: 'Transform your idea into a viable product through comprehensive market research, customer validation, and strategic MVP development.',
                icon: '🌱',
                gradient: 'from-green-500/20 to-emerald-500/20',
                border: 'border-green-500/30',
              },
              {
                id: 'growth',
                title: 'Growth & Scaling',
                description: 'Access premium resources and expert guidance to rapidly scale your business, optimize operations, and expand your customer base.',
                icon: '🌿',
                gradient: 'from-blue-500/20 to-cyan-500/20',
                border: 'border-blue-500/30',
              },
              {
                id: 'expansion',
                title: 'Market Expansion',
                description: 'Expand your business nationally and internationally with our strategic support, partnership network, and global market insights.',
                icon: '🌳',
                gradient: 'from-purple-500/20 to-pink-500/20',
                border: 'border-purple-500/30',
              },
            ].map((item, index) => (
              <motion.div
                key={item.id}
                initial={{ opacity: 0, x: -50 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                viewport={{ once: true }}
                whileHover={{ scale: 1.02, y: -5 }}
                className={`group relative bg-gradient-to-br ${item.gradient} backdrop-blur-xl border ${item.border} rounded-2xl p-8 hover:shadow-2xl hover:shadow-purple-500/10 transition-all duration-500`}
              >
                <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent rounded-2xl" />
                <div className="relative z-10">
                  <div className="text-5xl mb-6 group-hover:scale-110 transition-transform duration-300">{item.icon}</div>
                  <h3 className="text-2xl font-bold mb-4 text-white group-hover:text-purple-200 transition-colors duration-300">
                    {item.title}
                  </h3>
                  <p className="text-gray-300 leading-relaxed text-lg">
                    {item.description}
                  </p>
                </div>
              </motion.div>
            ))}
          </div>

          {/* Right Side - Spline 3D Scene */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 1, delay: 0.4 }}
            viewport={{ once: true }}
            className="relative"
          >
            <div className="relative h-[600px] lg:h-[700px] rounded-3xl overflow-hidden bg-gradient-to-br from-purple-900/20 via-black/40 to-purple-900/20 backdrop-blur-xl border border-purple-500/20 shadow-2xl shadow-purple-500/10">
              {/* Decorative Elements */}
              <div className="absolute inset-0 bg-gradient-to-br from-white/5 via-transparent to-purple-500/5" />
              <div className="absolute top-4 left-4 w-3 h-3 bg-green-400 rounded-full animate-pulse" />
              <div className="absolute top-4 left-10 w-3 h-3 bg-yellow-400 rounded-full animate-pulse delay-100" />
              <div className="absolute top-4 left-16 w-3 h-3 bg-red-400 rounded-full animate-pulse delay-200" />

              {/* Spline Component */}
              <div className="relative z-10 w-full h-full">
                <Spline
                  scene="https://prod.spline.design/NWd4lFwo4mAVWR2n/scene.splinecode"
                />
              </div>

              {/* Overlay Gradient */}
              <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent pointer-events-none" />
            </div>

            {/* Floating Label */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.8 }}
              viewport={{ once: true }}
              className="absolute -bottom-6 left-1/2 transform -translate-x-1/2 bg-gradient-to-r from-purple-600 to-purple-700 px-6 py-3 rounded-full border border-purple-400/30 shadow-lg"
            >
              <span className="text-white font-medium">Interactive 3D Experience</span>
            </motion.div>
          </motion.div>
        </div>

        {/* Additional Spline Scene at Bottom */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 1, delay: 0.6 }}
          viewport={{ once: true }}
          className="mt-32"
        >
          <div className="text-center mb-12">
            <motion.h3
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.7 }}
              viewport={{ once: true }}
              className="text-3xl md:text-4xl font-bold mb-4 bg-gradient-to-r from-purple-400 to-purple-600 bg-clip-text text-transparent"
            >
              Experience Innovation in 3D
            </motion.h3>
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.8 }}
              viewport={{ once: true }}
              className="text-lg text-gray-300 max-w-2xl mx-auto"
            >
              Explore our immersive 3D environment that showcases the future of business innovation
            </motion.p>
          </div>

          <div className="relative w-full h-[80vh] min-h-[600px] rounded-3xl overflow-hidden bg-gradient-to-br from-purple-900/30 via-black/50 to-purple-900/30 backdrop-blur-xl border border-purple-500/30 shadow-2xl shadow-purple-500/20">
            {/* Background Pattern */}
            <div className="absolute inset-0 opacity-10">
              <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(120,119,198,0.3),transparent_50%)]" />
              <div className="absolute inset-0 bg-[linear-gradient(45deg,transparent_30%,rgba(120,119,198,0.1)_50%,transparent_70%)]" />
            </div>

            {/* Decorative Corner Elements */}
            <div className="absolute top-6 left-6 flex space-x-2">
              <div className="w-4 h-4 bg-red-500 rounded-full opacity-80"></div>
              <div className="w-4 h-4 bg-yellow-500 rounded-full opacity-80"></div>
              <div className="w-4 h-4 bg-green-500 rounded-full opacity-80"></div>
            </div>

            {/* Main Spline Component */}
            <div className="relative z-10 w-full h-full">
              <Spline
                scene="https://prod.spline.design/NWd4lFwo4mAVWR2n/scene.splinecode"
              />
            </div>

            {/* Subtle Overlay */}
            <div className="absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-transparent pointer-events-none" />

            {/* Bottom Info Bar */}
            <div className="absolute bottom-6 left-1/2 transform -translate-x-1/2 flex items-center space-x-4 bg-black/60 backdrop-blur-md px-6 py-3 rounded-full border border-purple-500/30">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              <span className="text-white text-sm font-medium">Interactive 3D Experience</span>
              <div className="w-2 h-2 bg-purple-400 rounded-full animate-pulse delay-500"></div>
            </div>
          </div>
        </motion.div>

      </div>
    </div>
  );
}
