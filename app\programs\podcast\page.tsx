'use client';

import React from 'react';
import { motion } from 'framer-motion';
import Link from 'next/link';
import { BokehBackground } from '@/components/ui/aceternity/bokeh-background';
import { ArrowLeft, Play, Headphones, Mic } from 'lucide-react';

export default function PodcastProgramPage() {
  return (
    <>
      {/* Hero Section */}
      <section className="relative h-[60vh] overflow-hidden bg-black">
        {/* Background Image */}
        <div className="absolute inset-0">
          <div
            className="absolute inset-0 bg-cover bg-center opacity-70"
            style={{
              backgroundImage: 'url(/images/programs/1.jpg)',
              backgroundPosition: 'center center'
            }}
          />
          <div className="absolute inset-0 bg-gradient-to-b from-black/70 via-black/50 to-black" />
        </div>

        {/* Content */}
        <div className="relative z-10 h-full flex flex-col items-center justify-center text-center px-4">
          <Link 
            href="/programs" 
            className="absolute top-8 left-8 text-white/80 hover:text-white flex items-center transition-colors"
          >
            <ArrowLeft className="mr-2 h-5 w-5" />
            Буцах
          </Link>
          
          <motion.h1
            className="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-6 max-w-4xl"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            INNOHUB PODCAST
          </motion.h1>

          <motion.div
            className="w-24 h-1 bg-primary mb-8"
            initial={{ width: 0 }}
            animate={{ width: 96 }}
            transition={{ duration: 1, delay: 0.5 }}
          />

          <motion.p
            className="text-xl text-white/80 max-w-2xl mb-8"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.3 }}
          >
            Гарааны бизнес эрхлэгч, туршлагатай оюуллуулагч, үүсгэн байгуулагчийн олдоо оноо, туршлагуудаас хуваалцдаг Иннохаб подкаст сонс! дугаараг үзэгдэж хүрдэг.
          </motion.p>
        </div>
      </section>

      {/* Podcast Episodes Section */}
      <BokehBackground
        className="py-24"
        colors={['#ffcc33', '#ffaa00', '#ff8800', '#ffffff']}
        density={30}
      >
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <motion.div
              className="bg-black/30 backdrop-blur-md rounded-xl border border-white/10 p-8 mb-12"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
            >
              <h2 className="text-2xl md:text-3xl font-bold text-white mb-6">Подкастын тухай</h2>
              <p className="text-white/80 mb-6">
                InnoHub Podcast нь гарааны бизнес эрхлэгч, туршлагатай хөрөнгө оруулагч, үүсгэн байгуулагчдын туршлага, мэдлэгийг хуваалцах зорилготой подкаст юм. Бид долоо хоног бүр шинэ дугаар хүргэж, бизнесийн ертөнцийн сонирхолтой түүхүүдийг хуваалцдаг.
              </p>
              <div className="flex flex-wrap gap-4 mb-6">
                <div className="flex items-center text-white/80">
                  <Headphones className="h-5 w-5 mr-2" />
                  <span>50+ дугаар</span>
                </div>
                <div className="flex items-center text-white/80">
                  <Mic className="h-5 w-5 mr-2" />
                  <span>100+ зочид</span>
                </div>
                <div className="flex items-center text-white/80">
                  <Play className="h-5 w-5 mr-2" />
                  <span>10,000+ сонсогч</span>
                </div>
              </div>
              <p className="text-white/80">
                Манай подкастыг дараах платформуудаар сонсох боломжтой:
              </p>
              <ul className="list-disc pl-6 text-white/80 space-y-2 mt-4">
                <li>Spotify</li>
                <li>Apple Podcasts</li>
                <li>Google Podcasts</li>
                <li>YouTube</li>
              </ul>
            </motion.div>

            {/* Recent Episodes */}
            <h2 className="text-2xl md:text-3xl font-bold text-white mb-8 text-center">Сүүлийн дугаарууд</h2>
            
            <div className="space-y-6 mb-12">
              {/* Episode 1 */}
              <motion.div
                className="bg-black/30 backdrop-blur-md rounded-xl border border-white/10 p-6 hover:border-primary/50 transition-colors"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.1 }}
                whileHover={{ y: -5 }}
              >
                <div className="flex items-center gap-4">
                  <div className="w-16 h-16 rounded-full bg-primary/20 flex items-center justify-center flex-shrink-0">
                    <Play className="h-8 w-8 text-primary" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-white">Дугаар #50: Стартап экосистемийн хөгжил</h3>
                    <p className="text-white/60 text-sm">2024 оны 5-р сарын 15 | 45 минут</p>
                  </div>
                </div>
                <p className="text-white/80 mt-4">
                  Энэ дугаарт бид Монголын стартап экосистемийн өнөөгийн байдал, ирээдүйн чиг хандлагын талаар ярилцлаа.
                </p>
                <button className="mt-4 text-primary hover:text-primary/80 transition-colors flex items-center">
                  Сонсох <ArrowLeft className="ml-2 h-4 w-4 rotate-180" />
                </button>
              </motion.div>

              {/* Episode 2 */}
              <motion.div
                className="bg-black/30 backdrop-blur-md rounded-xl border border-white/10 p-6 hover:border-primary/50 transition-colors"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.2 }}
                whileHover={{ y: -5 }}
              >
                <div className="flex items-center gap-4">
                  <div className="w-16 h-16 rounded-full bg-primary/20 flex items-center justify-center flex-shrink-0">
                    <Play className="h-8 w-8 text-primary" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-white">Дугаар #49: Хөрөнгө оруулалт татах арга замууд</h3>
                    <p className="text-white/60 text-sm">2024 оны 5-р сарын 1 | 52 минут</p>
                  </div>
                </div>
                <p className="text-white/80 mt-4">
                  Энэ дугаарт бид гарааны компаниуд хөрөнгө оруулалт татах арга замууд, анхаарах зүйлсийн талаар ярилцлаа.
                </p>
                <button className="mt-4 text-primary hover:text-primary/80 transition-colors flex items-center">
                  Сонсох <ArrowLeft className="ml-2 h-4 w-4 rotate-180" />
                </button>
              </motion.div>
            </div>

            <motion.div
              className="text-center"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.8, delay: 0.5 }}
            >
              <Link href="#" className="px-8 py-3 bg-primary text-white rounded-full font-medium hover:bg-primary/90 transition-colors duration-300 shadow-lg shadow-primary/20 inline-block">
                Бүх дугаарууд үзэх
              </Link>
            </motion.div>
          </div>
        </div>
      </BokehBackground>
    </>
  );
}
