'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { AnimatedGradientBackground } from '@/components/ui/animated-gradient-background';
import { useLanguage } from '@/lib/context/language-context';
import { Eye, EyeOff, Github, Mail, User } from 'lucide-react';
import { cn } from '@/lib/utils';

export default function SignupPage() {
  const { t, isLoaded } = useLanguage();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    password: '',
    agreeToTerms: false,
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleCheckboxChange = (checked: boolean) => {
    setFormData((prev) => ({ ...prev, agreeToTerms: checked }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    
    // Simulate API call
    setTimeout(() => {
      setIsLoading(false);
      // Redirect to home page after successful signup
      router.push('/');
    }, 1500);
  };

  return (
    <AnimatedGradientBackground className="min-h-screen flex items-center justify-center py-20">
      <div className="w-full max-w-md px-4">
        <Card className="border-primary/20 bg-black/60 backdrop-blur-md">
          <CardHeader className="space-y-1 text-center">
            <CardTitle className="text-2xl font-bold tracking-tight">
              {isLoaded ? t('auth.createAccount') : 'Create an account'}
            </CardTitle>
            <CardDescription className="text-muted-foreground">
              {isLoaded ? t('auth.enterDetails') : 'Enter your details to create your account'}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-6">
              <div className="grid gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="name">{isLoaded ? t('auth.name') : 'Name'}</Label>
                  <div className="relative">
                    <User className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="name"
                      name="name"
                      placeholder={isLoaded ? t('auth.namePlaceholder') : 'Your name'}
                      type="text"
                      autoCapitalize="words"
                      autoComplete="name"
                      className="pl-10"
                      value={formData.name}
                      onChange={handleChange}
                      required
                    />
                  </div>
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="email">{isLoaded ? t('auth.email') : 'Email'}</Label>
                  <div className="relative">
                    <Mail className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="email"
                      name="email"
                      placeholder={isLoaded ? t('auth.emailPlaceholder') : '<EMAIL>'}
                      type="email"
                      autoCapitalize="none"
                      autoComplete="email"
                      autoCorrect="off"
                      className="pl-10"
                      value={formData.email}
                      onChange={handleChange}
                      required
                    />
                  </div>
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="password">{isLoaded ? t('auth.password') : 'Password'}</Label>
                  <div className="relative">
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute right-3 top-3 text-muted-foreground hover:text-foreground"
                      aria-label={showPassword ? 'Hide password' : 'Show password'}
                    >
                      {showPassword ? (
                        <EyeOff className="h-4 w-4" />
                      ) : (
                        <Eye className="h-4 w-4" />
                      )}
                    </button>
                    <Input
                      id="password"
                      name="password"
                      type={showPassword ? 'text' : 'password'}
                      autoCapitalize="none"
                      autoComplete="new-password"
                      className="pr-10"
                      value={formData.password}
                      onChange={handleChange}
                      required
                    />
                  </div>
                  <p className="text-xs text-muted-foreground">
                    {isLoaded ? t('auth.passwordRequirements') : 'Password must be at least 8 characters long'}
                  </p>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="terms"
                    checked={formData.agreeToTerms}
                    onCheckedChange={handleCheckboxChange}
                    required
                  />
                  <label
                    htmlFor="terms"
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    {isLoaded ? t('auth.agreeToTerms') : 'I agree to the'}{' '}
                    <Link
                      href="/terms"
                      className="text-primary hover:text-primary/80 underline underline-offset-4"
                    >
                      {isLoaded ? t('auth.termsOfService') : 'Terms of Service'}
                    </Link>{' '}
                    {isLoaded ? t('auth.and') : 'and'}{' '}
                    <Link
                      href="/privacy"
                      className="text-primary hover:text-primary/80 underline underline-offset-4"
                    >
                      {isLoaded ? t('auth.privacyPolicy') : 'Privacy Policy'}
                    </Link>
                  </label>
                </div>
              </div>
              <Button
                className="w-full bg-primary hover:bg-primary/90"
                onClick={handleSubmit}
                disabled={isLoading || !formData.agreeToTerms}
              >
                {isLoading ? (
                  <div className="flex items-center justify-center">
                    <div className="h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                    {isLoaded ? t('auth.creatingAccount') : 'Creating account...'}
                  </div>
                ) : (
                  isLoaded ? t('auth.createAccount') : 'Create account'
                )}
              </Button>
              <div className="relative">
                <div className="absolute inset-0 flex items-center">
                  <span className="w-full border-t border-primary/20"></span>
                </div>
                <div className="relative flex justify-center text-xs uppercase">
                  <span className="bg-black px-2 text-muted-foreground">
                    {isLoaded ? t('auth.orContinueWith') : 'Or continue with'}
                  </span>
                </div>
              </div>
              <Button variant="outline" className="border-primary/20 hover:bg-primary/10">
                <Github className="mr-2 h-4 w-4" />
                Github
              </Button>
            </div>
          </CardContent>
          <CardFooter className="flex flex-col">
            <p className="mt-2 text-center text-sm text-muted-foreground">
              {isLoaded ? t('auth.alreadyHaveAccount') : 'Already have an account?'}{' '}
              <Link
                href="/auth/login"
                className="font-medium text-primary hover:text-primary/80"
              >
                {isLoaded ? t('auth.signIn') : 'Sign in'}
              </Link>
            </p>
          </CardFooter>
        </Card>
      </div>
    </AnimatedGradientBackground>
  );
}
