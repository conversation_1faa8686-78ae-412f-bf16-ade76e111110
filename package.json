{"name": "innohub", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build --no-lint", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-hover-card": "^1.1.13", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-progress": "^1.1.6", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-tabs": "^1.1.11", "@studio-freight/lenis": "^1.0.42", "@tabler/icons-react": "^3.31.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.10.1", "gsap": "^3.13.0", "lucide-react": "^0.508.0", "next": "15.3.2", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^3.2.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.2", "tailwindcss": "^4", "tw-animate-css": "^1.2.9", "typescript": "^5"}}