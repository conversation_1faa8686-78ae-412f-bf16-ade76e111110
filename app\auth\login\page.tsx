'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { AnimatedGradientBackground } from '@/components/ui/animated-gradient-background';
import { useLanguage } from '@/lib/context/language-context';
import { Eye, EyeOff, Github, Mail } from 'lucide-react';
import { cn } from '@/lib/utils';

export default function LoginPage() {
  const { t, isLoaded } = useLanguage();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    rememberMe: false,
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleCheckboxChange = (checked: boolean) => {
    setFormData((prev) => ({ ...prev, rememberMe: checked }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    // Simulate API call
    setTimeout(() => {
      setIsLoading(false);
      // Redirect to courses page after successful login
      router.push('/courses');
    }, 1500);
  };

  return (
    <AnimatedGradientBackground className="min-h-screen flex items-center justify-center py-20">
      <div className="w-full max-w-md px-4">
        <Card className="border-primary/20 bg-black/60 backdrop-blur-md">
          <CardHeader className="space-y-1 text-center">
            <div className="flex justify-center mb-4">
              <div className="bg-primary/10 p-3 rounded-full">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary h-6 w-6">
                  <path d="M18 3a3 3 0 0 0-3 3v12a3 3 0 0 0 3 3 3 3 0 0 0 3-3 3 3 0 0 0-3-3H6a3 3 0 0 0-3 3 3 3 0 0 0 3 3 3 3 0 0 0 3-3V6a3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3h12a3 3 0 0 0 3-3 3 3 0 0 0-3-3z"></path>
                </svg>
              </div>
            </div>
            <CardTitle className="text-2xl font-bold tracking-tight">
              {isLoaded ? t('auth.loginCourse') : 'Access Your Courses'}
            </CardTitle>
            <CardDescription className="text-muted-foreground">
              {isLoaded ? t('auth.enterCredentialsCourse') : 'Enter your credentials to access exclusive courses and learning materials'}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-6">
              <div className="grid gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="email">{isLoaded ? t('auth.email') : 'Email'}</Label>
                  <div className="relative">
                    <Mail className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="email"
                      name="email"
                      placeholder={isLoaded ? t('auth.emailPlaceholder') : '<EMAIL>'}
                      type="email"
                      autoCapitalize="none"
                      autoComplete="email"
                      autoCorrect="off"
                      className="pl-10"
                      value={formData.email}
                      onChange={handleChange}
                      required
                    />
                  </div>
                </div>
                <div className="grid gap-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="password">{isLoaded ? t('auth.password') : 'Password'}</Label>
                    <Link
                      href="/auth/forgot-password"
                      className="text-sm font-medium text-primary hover:text-primary/80"
                    >
                      {isLoaded ? t('auth.forgotPassword') : 'Forgot password?'}
                    </Link>
                  </div>
                  <div className="relative">
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute right-3 top-3 text-muted-foreground hover:text-foreground"
                      aria-label={showPassword ? 'Hide password' : 'Show password'}
                    >
                      {showPassword ? (
                        <EyeOff className="h-4 w-4" />
                      ) : (
                        <Eye className="h-4 w-4" />
                      )}
                    </button>
                    <Input
                      id="password"
                      name="password"
                      type={showPassword ? 'text' : 'password'}
                      autoCapitalize="none"
                      autoComplete="current-password"
                      className="pr-10"
                      value={formData.password}
                      onChange={handleChange}
                      required
                    />
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="remember"
                    checked={formData.rememberMe}
                    onCheckedChange={handleCheckboxChange}
                  />
                  <label
                    htmlFor="remember"
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    {isLoaded ? t('auth.rememberMe') : 'Remember me'}
                  </label>
                </div>
              </div>
              <Button
                className="w-full bg-primary hover:bg-primary/90"
                onClick={handleSubmit}
                disabled={isLoading}
              >
                {isLoading ? (
                  <div className="flex items-center justify-center">
                    <div className="h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                    {isLoaded ? t('auth.signingIn') : 'Signing in...'}
                  </div>
                ) : (
                  isLoaded ? t('auth.accessCourses') : 'Access Courses'
                )}
              </Button>

              <div className="mt-4 text-center">
                <p className="text-sm text-muted-foreground">
                  {isLoaded ? t('auth.courseAccess') : 'By logging in, you\'ll get access to:'}
                </p>
                <ul className="mt-2 text-sm text-muted-foreground text-left list-disc pl-5 space-y-1">
                  <li>{isLoaded ? t('auth.exclusiveVideos') : 'Exclusive video tutorials'}</li>
                  <li>{isLoaded ? t('auth.liveWebinars') : 'Live webinars and workshops'}</li>
                  <li>{isLoaded ? t('auth.certificateCompletion') : 'Certificate of completion'}</li>
                </ul>
              </div>
              <div className="relative">
                <div className="absolute inset-0 flex items-center">
                  <span className="w-full border-t border-primary/20"></span>
                </div>
                <div className="relative flex justify-center text-xs uppercase">
                  <span className="bg-black px-2 text-muted-foreground">
                    {isLoaded ? t('auth.orContinueWith') : 'Or continue with'}
                  </span>
                </div>
              </div>
              <Button variant="outline" className="border-primary/20 hover:bg-primary/10">
                <Github className="mr-2 h-4 w-4" />
                Github
              </Button>
            </div>
          </CardContent>
          <CardFooter className="flex flex-col">
            <p className="mt-2 text-center text-sm text-muted-foreground">
              {isLoaded ? t('auth.noAccount') : "Don't have an account?"}{' '}
              <Link
                href="/auth/signup"
                className="font-medium text-primary hover:text-primary/80"
              >
                {isLoaded ? t('auth.signUp') : 'Sign up'}
              </Link>
            </p>
          </CardFooter>
        </Card>
      </div>
    </AnimatedGradientBackground>
  );
}
