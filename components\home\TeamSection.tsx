'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { useLanguage } from '@/lib/context/language-context';
import { TeamMemberCard } from '@/components/ui/team-member-card';

const teamMembers = [
  {
    name: '<PERSON>',
    role: 'Founder & CEO',
    bio: 'Serial entrepreneur with 15+ years experience in tech startups',
    image: '/images/Team/team1.jpg',
    featured: false,
  },
  {
    name: '<PERSON>',
    role: 'Investment Director',
    bio: 'Former Google engineer with expertise in AI and machine learning',
    image: '/images/Team/team2.jpg',
    featured: false,
  },
  {
    name: '<PERSON>',
    role: 'Program Manager',
    bio: 'Ex-VC with $200M+ in successful investments across 30+ startups',
    image: '/images/Team/team3.jpg',
    featured: true,
  },
  {
    name: '<PERSON>',
    role: 'Operations Lead',
    bio: 'Operations expert who scaled 3 unicorns in the last decade',
    image: '/images/Team/team4.jpg',
    featured: false,
  },
  {
    name: '<PERSON>',
    role: 'Mentor Lead',
    bio: 'Startup advisor who has mentored 100+ founders to success',
    image: '/images/Team/team5.jpg',
    featured: false,
  },
  {
    name: '<PERSON>',
    role: 'Marketing Director',
    bio: 'Digital marketing expert with a focus on growth strategies',
    image: '/images/Team/team6.jpg',
    featured: false,
  },
];

export default function TeamSection() {
  const { t } = useLanguage();

  return (
    <section className="py-24 bg-black">
      <div className="w-full px-4 sm:px-6">
        <div className="text-center mb-16">
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
            className="text-3xl md:text-4xl font-bold mb-4 text-white"
          >
            {t('team.title')}
          </motion.h2>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            viewport={{ once: true }}
            className="text-xl text-muted-foreground max-w-3xl mx-auto"
          >
            {t('team.subtitle')}
          </motion.p>
        </div>

        {/* New Team Grid Layout with TeamMemberCard component - Full Width */}
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4 w-full will-change-transform">
          {teamMembers.map((member, index) => (
            <div
              key={`team-member-${member.name}`}
              className="relative will-change-transform"
              style={{
                containIntrinsicSize: '0 360px', // Hint for browser about size
                contentVisibility: 'auto' // Improve rendering performance
              }}
            >
              <TeamMemberCard
                name={member.name}
                role={member.role}
                image={member.image}
                index={index}
                className="h-[280px] md:h-[320px] lg:h-[360px]" // Reduced size by ~20%
              />
            </div>
          ))}
        </div>

        <div className="text-center mt-16">
          <Button size="lg" variant="outline" className="border-primary/20 hover:bg-primary/10" asChild>
            <Link href="/team">{t('team.meetFullTeam')}</Link>
          </Button>
        </div>
      </div>
    </section>
  );
}
