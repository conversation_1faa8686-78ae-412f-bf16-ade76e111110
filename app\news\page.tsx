'use client';

<<<<<<< HEAD
import { useState, useRef } from 'react';
import { motion, useScroll, useTransform } from 'framer-motion';
import Link from 'next/link';
import { AnimatedText } from '@/components/ui/animated-text';
import { TextReveal } from '@/components/ui/aceternity/text-reveal';
import { GlowingStarsBackground } from '@/components/ui/aceternity/glowing-stars';
import { Spotlight } from '@/components/ui/aceternity/spotlight';
import { LightbulbPlant } from '@/components/ui/aceternity/lightbulb-plant';
import { MovingBorder } from '@/components/ui/aceternity/moving-border';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardFooter } from '@/components/ui/card';
import { ArrowRight, Calendar, Search, Filter, Lightbulb } from 'lucide-react';
=======
import Link from 'next/link';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { useLanguage } from '@/lib/context/language-context';
>>>>>>> 0e385ae4e164af8691d0145eafd556f21275222f

// Define the type for news articles
interface NewsArticle {
  id: number;
  title: string;
  excerpt: string;
  date: string;
  category: string;
  image: string;
  slug: string;
}

<<<<<<< HEAD
const newsArticles: NewsArticle[] = [
  {
    id: 1,
    title: 'InnoHub Announces New Startup Accelerator Program',
    excerpt: 'Applications are now open for our latest accelerator program focused on AI and machine learning startups.',
    date: 'May 5, 2024',
    category: 'Program',
    image: '/placeholder-news-1.jpg',
    slug: 'new-accelerator-program',
  },
  {
    id: 2,
    title: 'Portfolio Company Secures $10M Series A Funding',
    excerpt: 'TechNova, a graduate of our 2022 accelerator program, has successfully raised $10M in Series A funding led by Sequoia Capital.',
    date: 'April 28, 2024',
    category: 'Success Story',
    image: '/placeholder-news-2.jpg',
    slug: 'technova-funding',
  },
  {
    id: 3,
    title: 'InnoHub Expands to New York City',
    excerpt: "We're excited to announce the opening of our new office in New York City, expanding our presence to the East Coast.",
    date: 'April 15, 2024',
    category: 'Company News',
    image: '/placeholder-news-3.jpg',
    slug: 'nyc-expansion',
  },
  {
    id: 4,
    title: 'Innovation Workshop Series Launches Next Month',
    excerpt: 'Join us for a series of hands-on workshops designed to help entrepreneurs develop innovative solutions to real-world problems.',
    date: 'April 10, 2024',
    category: 'Events',
    image: '/placeholder-news-4.jpg',
    slug: 'workshop-series',
  },
  {
    id: 5,
    title: 'InnoHub Partners with Leading University for Research Initiative',
    excerpt: "We've partnered with Stanford University to launch a research initiative focused on sustainable technology innovation.",
    date: 'March 22, 2024',
    category: 'Partnership',
    image: '/placeholder-news-5.jpg',
    slug: 'university-partnership',
  },
  {
    id: 6,
    title: 'Annual Innovation Summit Announced',
    excerpt: 'Save the date for our annual Innovation Summit, bringing together industry leaders, investors, and startups for a day of learning and networking.',
    date: 'March 15, 2024',
    category: 'Events',
    image: '/placeholder-news-6.jpg',
    slug: 'innovation-summit',
  },
];

const categories = ['All', 'Program', 'Success Story', 'Company News', 'Events', 'Partnership'];

// Custom NewsCard component with lightbulb effect
const NewsCard = ({ article, index }: { article: NewsArticle; index: number }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      viewport={{ once: true }}
      whileHover={{ y: -5 }}
      className="group"
    >
      <Link href={`/news/${article.slug}`} className="h-full block">
        <Card className="overflow-hidden h-full flex flex-col border-border/50 bg-black/40 backdrop-blur-sm transition-all duration-300 hover:border-primary/50 hover:shadow-lg relative">
          {/* Lightbulb glow effect */}
          <div className="absolute -top-6 left-1/2 -translate-x-1/2 w-12 h-12 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
            <div className="relative w-full h-full">
              <motion.div
                className="absolute inset-0 rounded-full bg-yellow-300/30 blur-md"
                animate={{
                  scale: [1, 1.2, 1],
                  opacity: [0.5, 0.8, 0.5]
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  repeatType: 'reverse',
                }}
              />
              <div className="absolute inset-0 flex items-center justify-center">
                <Lightbulb className="h-6 w-6 text-yellow-300" />
              </div>
            </div>
          </div>

          {/* Card content */}
          <div className="relative aspect-[16/9] bg-gradient-to-b from-primary/20 to-black/40 overflow-hidden">
            <motion.div
              className="absolute inset-0 bg-black/50 flex items-center justify-center"
              whileHover={{ opacity: 0.7 }}
              transition={{ duration: 0.3 }}
            >
              <div className="text-2xl font-bold text-white/80">{article.id.toString().padStart(2, '0')}</div>
            </motion.div>
            <motion.div
              className="absolute bottom-0 left-0 right-0 h-1/3 bg-gradient-to-t from-black to-transparent"
              initial={{ opacity: 0.7 }}
              whileHover={{ opacity: 0.9 }}
            />
          </div>

          <CardContent className="flex-grow pt-6 z-10">
            <div className="flex items-center gap-2 mb-3">
              <motion.span
                className="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors border-transparent bg-primary/20 text-primary"
                whileHover={{ scale: 1.05, backgroundColor: 'rgba(var(--primary), 0.3)' }}
              >
                {article.category}
              </motion.span>
              <div className="flex items-center text-xs text-muted-foreground">
                <Calendar className="h-3 w-3 mr-1" />
                {article.date}
              </div>
            </div>
            <h3 className="font-semibold text-xl mb-2 group-hover:text-primary transition-colors duration-300">{article.title}</h3>
            <p className="text-muted-foreground text-sm">{article.excerpt}</p>
          </CardContent>

          <CardFooter className="pt-0 pb-6">
            <Button variant="ghost" className="group p-0" asChild>
              <span>
                Read More
                <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
              </span>
            </Button>
          </CardFooter>

          {/* Bottom glow effect */}
          <motion.div
            className="absolute bottom-0 left-0 right-0 h-1/4 opacity-0 group-hover:opacity-100 transition-opacity duration-500"
            style={{
              background: 'radial-gradient(ellipse at bottom, rgba(var(--primary-rgb), 0.15), transparent 80%)'
            }}
          />
        </Card>
      </Link>
    </motion.div>
  );
};

=======
>>>>>>> 0e385ae4e164af8691d0145eafd556f21275222f
export default function NewsPage() {
  const { t } = useLanguage();

  const newsArticles: NewsArticle[] = [
    {
      id: 1,
      title: t('news.article1.title'),
      excerpt: t('news.article1.excerpt'),
      date: 'May 15, 2024',
      category: 'Finance',
      image: '/images/programs/1.jpg',
      slug: 'market-analysis',
    },
    {
      id: 2,
      title: t('news.article2.title'),
      excerpt: t('news.article2.excerpt'),
      date: 'May 10, 2024',
      category: 'Finance',
      image: '/images/programs/2.jpg',
      slug: 'mongolia-finance-analysis',
    },
    {
      id: 3,
      title: t('news.article3.title'),
      excerpt: t('news.article3.excerpt'),
      date: 'May 5, 2024',
      category: 'Data',
      image: '/images/programs/plant-bulbs.jpg',
      slug: 'data-processing-solongo',
    },
    {
      id: 4,
      title: t('news.article4.title'),
      excerpt: t('news.article4.excerpt'),
      date: 'April 28, 2024',
      category: 'International',
      image: '/images/programs/1.jpg',
      slug: 'international-market-entry',
    },
  ];

  const featuredArticle = {
    id: 5,
    title: t('news.featured.title'),
    excerpt: t('news.featured.excerpt'),
    date: 'June 5, 2024',
    category: 'Marketing',
    image: '/images/programs/2.jpg',
    slug: 'product-marketing-vs-marketing',
  };
  return (
<<<<<<< HEAD
    <>
      <GlowingStarsBackground className="min-h-[60vh] flex flex-col items-center justify-center pt-32 pb-20 relative overflow-hidden">
        <Spotlight className="absolute inset-0" />

        <motion.div
          ref={containerRef}
          style={{ opacity, scale }}
          className="container mx-auto px-4 relative z-10"
        >
          <div className="text-center max-w-3xl mx-auto">
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5 }}
              className="inline-block rounded-full bg-primary/10 px-3 py-1 text-sm font-medium text-primary mb-4"
            >
              News & Insights
            </motion.div>

            <AnimatedText
              text="Illuminating Innovation Stories"
              className="text-4xl md:text-5xl font-bold tracking-tight mb-6"
            />

            <AnimatedText
              text="Stay updated with our latest announcements, success stories, and upcoming events."
              className="text-xl text-muted-foreground"
              once
            />

            <div className="mt-12 flex justify-center">
              <div className="relative w-full max-w-md">
                <LightbulbPlant className="absolute -top-24 left-1/2 -translate-x-1/2 w-32 h-32 opacity-70" />
              </div>
            </div>
          </div>
        </motion.div>
      </GlowingStarsBackground>

      <section className="py-20 bg-black relative">
        <div className="absolute inset-0 bg-gradient-to-b from-primary/5 to-transparent opacity-50" />

        <div className="container mx-auto px-4 relative z-10">
          {/* Search and filter section */}
          <div className="mb-12 max-w-4xl mx-auto">
            <div className="flex flex-col md:flex-row gap-4 items-center justify-between bg-black/60 backdrop-blur-md p-4 rounded-xl border border-white/10">
              <div className="relative w-full md:w-auto flex-grow">
                <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  type="text"
                  placeholder="Search articles..."
                  className="pl-10 bg-black/50 border-white/10"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
              <div className="flex items-center gap-2 w-full md:w-auto overflow-x-auto pb-2 md:pb-0 no-scrollbar">
                <Filter className="h-4 w-4 text-primary hidden md:block" />
                <div className="flex gap-2">
                  {categories.map((category) => (
                    <Button
                      key={category}
                      variant={activeCategory === category ? "default" : "outline"}
                      onClick={() => setActiveCategory(category)}
                      size="sm"
                      className={`whitespace-nowrap ${activeCategory === category ? 'bg-primary/20 text-primary border-primary/30' : 'bg-black/50'}`}
                    >
                      {category}
                    </Button>
                  ))}
=======
    <div className="flex flex-col">
      {/* Hero section with interactive lightbulb and plant */}
      <div className="relative w-full bg-gradient-to-b from-black to-gray-900 py-24 overflow-hidden">
        <div className="absolute inset-0 bg-[url('/images/programs/plant-bulbs.jpg')] bg-cover bg-center opacity-40"></div>
        <div className="container mx-auto relative z-10 flex flex-col items-center justify-center text-white">
          <h1 className="text-4xl font-bold text-center mb-2">{t('news.hero.title')}</h1>
          <h2 className="text-3xl font-bold text-center mb-4">{t('news.hero.subtitle')}</h2>
          <h3 className="text-3xl font-bold text-center mb-6 text-white">{t('news.hero.tagline')}</h3>
        </div>
      </div>

      {/* News cards grid */}
      <div className="bg-black py-12">
        <div className="container mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {newsArticles.map((article) => (
              <Link href={`/news/${article.slug}`} key={article.id}>
                <div className="bg-gray-900 rounded-lg overflow-hidden border border-gray-800 hover:border-yellow-500/50 transition-all duration-300">
                  <div className="p-4">
                    <div className="h-40 bg-blue-900 rounded-lg overflow-hidden mb-4 relative">
                      <Image
                        src={article.image}
                        alt={article.title}
                        className="object-cover"
                        fill
                      />
                    </div>
                    <h3 className="text-white text-sm font-medium mb-2">{article.title}</h3>
                    <p className="text-gray-400 text-xs">{article.excerpt}</p>
                  </div>
>>>>>>> 0e385ae4e164af8691d0145eafd556f21275222f
                </div>
              </Link>
            ))}
          </div>

          {/* Carousel navigation */}
          <div className="flex justify-center mt-8 gap-2">
            <button className="w-8 h-8 rounded-full border border-gray-600 flex items-center justify-center text-gray-400">
              <ChevronLeft className="w-4 h-4" />
            </button>
            <button className="w-2 h-2 rounded-full bg-gray-600"></button>
            <button className="w-2 h-2 rounded-full bg-yellow-400"></button>
            <button className="w-2 h-2 rounded-full bg-gray-600"></button>
            <button className="w-8 h-8 rounded-full border border-gray-600 flex items-center justify-center text-gray-400">
              <ChevronRight className="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>

      {/* Featured article section */}
      <div className="bg-black py-12">
        <div className="container mx-auto">
          <div className="bg-gray-900 rounded-lg overflow-hidden">
            <div className="p-6">
              <div className="flex flex-col md:flex-row gap-6">
                <div className="md:w-1/3 relative h-60">
                  <Image
                    src={featuredArticle.image}
                    alt={featuredArticle.title}
                    className="rounded-lg object-cover"
                    fill
                  />
                </div>
                <div className="md:w-2/3">
                  <h2 className="text-white text-xl font-bold mb-3">{featuredArticle.title}</h2>
                  <p className="text-gray-400 mb-4">{featuredArticle.excerpt}</p>
                  <div className="text-xs text-gray-500 mb-6">
                    {t('news.published')} {featuredArticle.date} | {t('news.category')} {featuredArticle.category}
                  </div>
                  <Link href={`/news/${featuredArticle.slug}`}>
                    <Button className="bg-yellow-500 hover:bg-yellow-600 text-black">
                      {t('news.details.button')}
                    </Button>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
