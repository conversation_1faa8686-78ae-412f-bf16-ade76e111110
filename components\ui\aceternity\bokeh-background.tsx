'use client';

import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';

interface BokehProps {
  children: React.ReactNode;
  className?: string;
  density?: number;
  speed?: number;
  colors?: string[];
}

export const BokehBackground = ({
  children,
  className,
  density = 30,
  speed = 3,
  colors = ['#ffcc33', '#ffaa00', '#ff8800', '#ffffff'],
}: BokehProps) => {
  const [bokehElements, setBokehElements] = useState<React.ReactNode[]>([]);

  useEffect(() => {
    const elements = [];
    for (let i = 0; i < density; i++) {
      const size = Math.random() * 80 + 20; // 20-100px
      const x = Math.random() * 100;
      const y = Math.random() * 100;
      const delay = Math.random() * 5;
      const duration = (Math.random() * 10 + 10) / speed;
      const color = colors[Math.floor(Math.random() * colors.length)];
      const opacity = Math.random() * 0.5 + 0.1; // 0.1-0.6

      elements.push(
        <motion.div
          key={i}
          className="absolute rounded-full blur-md"
          style={{
            width: `${size}px`,
            height: `${size}px`,
            left: `${x}%`,
            top: `${y}%`,
            backgroundColor: color,
            opacity,
          }}
          animate={{
            x: [
              Math.random() * 100 - 50,
              Math.random() * 100 - 50,
              Math.random() * 100 - 50,
            ],
            y: [
              Math.random() * 100 - 50,
              Math.random() * 100 - 50,
              Math.random() * 100 - 50,
            ],
            opacity: [opacity, opacity * 1.5, opacity],
          }}
          transition={{
            duration,
            repeat: Infinity,
            repeatType: 'reverse',
            delay,
            ease: 'easeInOut',
          }}
        />
      );
    }
    setBokehElements(elements);
  }, [density, speed, colors]);

  return (
    <div className={cn('relative overflow-hidden', className)}>
      <div className="absolute inset-0 z-0">
        {bokehElements}
        <div className="absolute inset-0 bg-black/70 backdrop-blur-sm z-10" />
      </div>
      <div className="relative z-20">{children}</div>
    </div>
  );
};

export const GlassCard = ({
  children,
  className,
  hoverEffect = true,
}: {
  children: React.ReactNode;
  className?: string;
  hoverEffect?: boolean;
}) => {
  return (
    <motion.div
      className={cn(
        'relative backdrop-blur-md bg-white/5 border border-white/10 rounded-xl overflow-hidden',
        hoverEffect && 'transition-all duration-300 hover:bg-white/10 hover:shadow-lg',
        className
      )}
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      viewport={{ once: true }}
    >
      <div className="absolute inset-0 bg-gradient-to-br from-primary/10 to-transparent opacity-30" />
      <div className="relative z-10">{children}</div>
    </motion.div>
  );
};
