'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link';
import { useLanguage } from '@/lib/context/language-context';
import { Button } from '@/components/ui/button';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { 
  BookOpen, 
  Clock, 
  Award, 
  User, 
  ThumbsUp, 
  MessageSquare, 
  Share2, 
  Download, 
  Bookmark,
  ChevronLeft,
  PlayCircle,
  CheckCircle
} from 'lucide-react';

// Mock data for courses
const COURSES_DATA = [
  {
    id: 1,
    title: 'Introduction to Entrepreneurship',
    description: 'Learn the fundamentals of starting and growing a successful business',
    longDescription: 'This comprehensive course covers everything you need to know to start your entrepreneurial journey. From ideation to execution, you\'ll learn proven strategies for building a successful business from the ground up. Our expert instructors will guide you through the essential concepts, tools, and mindsets that successful entrepreneurs use to create thriving businesses.',
    thumbnail: '/images/courses/entrepreneurship.jpg',
    duration: '1h 45m',
    lessons: 12,
    level: 'Beginner',
    instructor: '<PERSON>',
    instructorTitle: 'Founder & CEO, Tech<PERSON>entures',
    instructorImage: '/images/instructors/john.jpg',
    category: 'Business',
    videoId: 'Dn-Yk5YRMD8',
    modules: [
      {
        title: 'Getting Started',
        lessons: [
          { title: 'Introduction to the Course', duration: '5:20', isCompleted: true, videoId: 'Dn-Yk5YRMD8' },
          { title: 'What is Entrepreneurship?', duration: '10:15', isCompleted: true, videoId: 'Dn-Yk5YRMD8' },
          { title: 'The Entrepreneurial Mindset', duration: '8:45', isCompleted: false, videoId: 'Dn-Yk5YRMD8' },
        ]
      },
      {
        title: 'Business Ideation',
        lessons: [
          { title: 'Finding Business Opportunities', duration: '12:30', isCompleted: false, videoId: 'Dn-Yk5YRMD8' },
          { title: 'Validating Your Idea', duration: '15:10', isCompleted: false, videoId: 'Dn-Yk5YRMD8' },
          { title: 'Market Research Fundamentals', duration: '18:25', isCompleted: false, videoId: 'Dn-Yk5YRMD8' },
        ]
      },
      {
        title: 'Business Planning',
        lessons: [
          { title: 'Creating a Business Plan', duration: '20:15', isCompleted: false, videoId: 'Dn-Yk5YRMD8' },
          { title: 'Financial Projections', duration: '16:40', isCompleted: false, videoId: 'Dn-Yk5YRMD8' },
          { title: 'Funding Options for Startups', duration: '14:55', isCompleted: false, videoId: 'Dn-Yk5YRMD8' },
        ]
      },
    ],
    progress: 16,
  },
  // Other courses would be defined here
];

export default function CoursePage() {
  const { t, isLoaded } = useLanguage();
  const params = useParams();
  const courseId = Number(params.id);
  
  const [course, setCourse] = useState<typeof COURSES_DATA[0] | null>(null);
  const [currentVideo, setCurrentVideo] = useState<string | null>(null);
  const [currentLessonTitle, setCurrentLessonTitle] = useState<string>('');
  const [isLoading, setIsLoading] = useState(true);
  
  useEffect(() => {
    // Simulate API call to fetch course data
    setIsLoading(true);
    setTimeout(() => {
      const foundCourse = COURSES_DATA.find(c => c.id === courseId);
      if (foundCourse) {
        setCourse(foundCourse);
        // Set the first video as the current video
        const firstModule = foundCourse.modules[0];
        const firstLesson = firstModule.lessons[0];
        setCurrentVideo(firstLesson.videoId);
        setCurrentLessonTitle(firstLesson.title);
      }
      setIsLoading(false);
    }, 500);
  }, [courseId]);
  
  const handleLessonClick = (videoId: string, title: string) => {
    setCurrentVideo(videoId);
    setCurrentLessonTitle(title);
    // Scroll to top on mobile
    if (window.innerWidth < 768) {
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }
  };
  
  if (isLoading) {
    return (
      <div className="min-h-screen bg-black pt-24 pb-16 flex items-center justify-center">
        <div className="animate-pulse flex flex-col items-center">
          <div className="h-8 w-32 bg-primary/20 rounded-full mb-4"></div>
          <div className="h-4 w-48 bg-gray-700/50 rounded mb-2"></div>
          <div className="h-4 w-40 bg-gray-700/50 rounded"></div>
        </div>
      </div>
    );
  }
  
  if (!course) {
    return (
      <div className="min-h-screen bg-black pt-24 pb-16 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-2">
            {isLoaded ? t('courses.notFound') : 'Course not found'}
          </h2>
          <p className="text-muted-foreground mb-6">
            {isLoaded ? t('courses.notFoundDesc') : 'The course you are looking for does not exist or has been removed.'}
          </p>
          <Button asChild>
            <Link href="/courses">
              <ChevronLeft className="mr-2 h-4 w-4" />
              {isLoaded ? t('courses.backToCourses') : 'Back to Courses'}
            </Link>
          </Button>
        </div>
      </div>
    );
  }
  
  return (
    <div className="min-h-screen bg-black pt-20 pb-16">
      <div className="container mx-auto px-4">
        <div className="flex flex-col lg:flex-row gap-8">
          {/* Video Player Section */}
          <div className="w-full lg:w-2/3">
            <div className="bg-black rounded-lg overflow-hidden">
              <div className="relative aspect-video">
                {currentVideo && (
                  <iframe
                    src={`https://www.youtube.com/embed/${currentVideo}?autoplay=0&rel=0`}
                    title={currentLessonTitle}
                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                    allowFullScreen
                    className="absolute inset-0 w-full h-full"
                  ></iframe>
                )}
              </div>
              
              <div className="p-4 border-b border-primary/10">
                <h1 className="text-2xl font-bold">{currentLessonTitle}</h1>
                <div className="flex items-center justify-between mt-4">
                  <div className="flex items-center gap-4">
                    <Button variant="ghost" size="sm" className="flex items-center gap-2">
                      <ThumbsUp className="h-4 w-4" />
                      <span>{isLoaded ? t('courses.like') : 'Like'}</span>
                    </Button>
                    <Button variant="ghost" size="sm" className="flex items-center gap-2">
                      <MessageSquare className="h-4 w-4" />
                      <span>{isLoaded ? t('courses.comment') : 'Comment'}</span>
                    </Button>
                    <Button variant="ghost" size="sm" className="flex items-center gap-2">
                      <Share2 className="h-4 w-4" />
                      <span>{isLoaded ? t('courses.share') : 'Share'}</span>
                    </Button>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button variant="ghost" size="sm" className="flex items-center gap-2">
                      <Download className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm" className="flex items-center gap-2">
                      <Bookmark className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>
              
              <Tabs defaultValue="about" className="p-4">
                <TabsList className="bg-background/5 mb-4">
                  <TabsTrigger 
                    value="about" 
                    className="data-[state=active]:bg-primary/20"
                  >
                    {isLoaded ? t('courses.about') : 'About'}
                  </TabsTrigger>
                  <TabsTrigger 
                    value="instructor" 
                    className="data-[state=active]:bg-primary/20"
                  >
                    {isLoaded ? t('courses.instructor') : 'Instructor'}
                  </TabsTrigger>
                  <TabsTrigger 
                    value="resources" 
                    className="data-[state=active]:bg-primary/20"
                  >
                    {isLoaded ? t('courses.resources') : 'Resources'}
                  </TabsTrigger>
                </TabsList>
                
                <TabsContent value="about">
                  <div className="space-y-4">
                    <div className="flex flex-wrap gap-4 mb-4">
                      <div className="flex items-center text-sm text-muted-foreground">
                        <Clock className="h-4 w-4 mr-2" />
                        <span>{course.duration}</span>
                      </div>
                      <div className="flex items-center text-sm text-muted-foreground">
                        <BookOpen className="h-4 w-4 mr-2" />
                        <span>{course.lessons} {isLoaded ? t('courses.lessons') : 'lessons'}</span>
                      </div>
                      <div className="flex items-center text-sm text-muted-foreground">
                        <Award className="h-4 w-4 mr-2" />
                        <span>{course.level}</span>
                      </div>
                    </div>
                    
                    <h3 className="text-lg font-semibold">
                      {isLoaded ? t('courses.description') : 'Description'}
                    </h3>
                    <p className="text-muted-foreground">{course.longDescription}</p>
                    
                    <h3 className="text-lg font-semibold mt-6">
                      {isLoaded ? t('courses.whatYouWillLearn') : 'What You Will Learn'}
                    </h3>
                    <ul className="grid grid-cols-1 md:grid-cols-2 gap-2">
                      <li className="flex items-start">
                        <CheckCircle className="h-5 w-5 text-primary mr-2 flex-shrink-0 mt-0.5" />
                        <span>Understand the fundamentals of entrepreneurship</span>
                      </li>
                      <li className="flex items-start">
                        <CheckCircle className="h-5 w-5 text-primary mr-2 flex-shrink-0 mt-0.5" />
                        <span>Develop a business idea from concept to execution</span>
                      </li>
                      <li className="flex items-start">
                        <CheckCircle className="h-5 w-5 text-primary mr-2 flex-shrink-0 mt-0.5" />
                        <span>Create a comprehensive business plan</span>
                      </li>
                      <li className="flex items-start">
                        <CheckCircle className="h-5 w-5 text-primary mr-2 flex-shrink-0 mt-0.5" />
                        <span>Understand different funding options for startups</span>
                      </li>
                    </ul>
                  </div>
                </TabsContent>
                
                <TabsContent value="instructor">
                  <div className="flex items-start gap-4">
                    <div className="relative w-16 h-16 rounded-full overflow-hidden flex-shrink-0">
                      <Image
                        src={course.instructorImage}
                        alt={course.instructor}
                        fill
                        className="object-cover"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.src = 'https://placehold.co/200x200/3a0647/ffffff?text=Instructor';
                        }}
                      />
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold">{course.instructor}</h3>
                      <p className="text-sm text-muted-foreground">{course.instructorTitle}</p>
                      <p className="mt-2 text-muted-foreground">
                        An experienced entrepreneur and educator with over 10 years of experience in the startup ecosystem.
                        Passionate about helping new entrepreneurs navigate the challenges of starting a business.
                      </p>
                    </div>
                  </div>
                </TabsContent>
                
                <TabsContent value="resources">
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold">
                      {isLoaded ? t('courses.downloadableResources') : 'Downloadable Resources'}
                    </h3>
                    <ul className="space-y-2">
                      <li>
                        <Button variant="link" className="p-0 h-auto text-primary">
                          <Download className="h-4 w-4 mr-2" />
                          Business Plan Template.pdf
                        </Button>
                      </li>
                      <li>
                        <Button variant="link" className="p-0 h-auto text-primary">
                          <Download className="h-4 w-4 mr-2" />
                          Market Research Worksheet.xlsx
                        </Button>
                      </li>
                      <li>
                        <Button variant="link" className="p-0 h-auto text-primary">
                          <Download className="h-4 w-4 mr-2" />
                          Financial Projections Template.xlsx
                        </Button>
                      </li>
                    </ul>
                  </div>
                </TabsContent>
              </Tabs>
            </div>
          </div>
          
          {/* Course Content Section */}
          <div className="w-full lg:w-1/3">
            <div className="bg-black/40 border border-primary/10 rounded-lg overflow-hidden">
              <div className="p-4 border-b border-primary/10">
                <h2 className="text-xl font-semibold">
                  {isLoaded ? t('courses.courseContent') : 'Course Content'}
                </h2>
                <div className="flex items-center justify-between mt-2">
                  <div className="text-sm text-muted-foreground">
                    {course.progress}% {isLoaded ? t('courses.completed') : 'completed'}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {course.modules.reduce((acc, module) => acc + module.lessons.length, 0)} {isLoaded ? t('courses.lessons') : 'lessons'}
                  </div>
                </div>
                <Progress value={course.progress} className="mt-2 h-2" />
              </div>
              
              <div className="max-h-[600px] overflow-y-auto">
                {course.modules.map((module, moduleIndex) => (
                  <div key={moduleIndex} className="border-b border-primary/10 last:border-b-0">
                    <div className="p-4 font-medium">
                      {module.title}
                    </div>
                    <div>
                      {module.lessons.map((lesson, lessonIndex) => (
                        <button
                          key={lessonIndex}
                          className={`w-full text-left p-4 hover:bg-primary/5 flex items-start gap-3 transition-colors ${currentLessonTitle === lesson.title ? 'bg-primary/10' : ''}`}
                          onClick={() => handleLessonClick(lesson.videoId, lesson.title)}
                        >
                          <div className={`rounded-full p-1 ${lesson.isCompleted ? 'bg-primary/20 text-primary' : 'bg-muted/20 text-muted-foreground'}`}>
                            {lesson.isCompleted ? (
                              <CheckCircle className="h-4 w-4" />
                            ) : (
                              <PlayCircle className="h-4 w-4" />
                            )}
                          </div>
                          <div className="flex-1">
                            <div className="text-sm font-medium">{lesson.title}</div>
                            <div className="text-xs text-muted-foreground mt-1">{lesson.duration}</div>
                          </div>
                        </button>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
