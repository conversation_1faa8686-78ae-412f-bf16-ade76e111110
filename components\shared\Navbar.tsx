'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { usePathname } from 'next/navigation';
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';
import { Menu, Globe } from 'lucide-react';
import { useLanguage } from '@/lib/context/language-context';
import { HoverBorderGradient } from '@/components/ui/hover-border-gradient';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';

const navLinks = [
  { href: '/', key: 'nav.home' },
  { href: '/about', key: 'nav.about' },
  { href: '/programs', key: 'nav.programs' },
  { href: '/investors', key: 'nav.investors' },
  { href: '/news', key: 'nav.news' },
];

export default function Navbar() {
  const [scrolled, setScrolled] = useState(false);
  const pathname = usePathname();
  const { t, language, setLanguage, isLoaded } = useLanguage();

  useEffect(() => {
    const handleScroll = () => {
      const isScrolled = window.scrollY > 10;
      if (isScrolled !== scrolled) {
        setScrolled(isScrolled);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, [scrolled]);

  return (
    <header
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
        scrolled ? 'bg-black/80 backdrop-blur-md border-b border-purple-500' : 'bg-transparent'
      }`}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 flex items-center justify-between">
        {/* Logo (Left) */}
        <Link href="/" className="flex items-center gap-2 z-10">
          <div className="relative w-10 h-10 rounded-full overflow-hidden">
            <Image
              src="/images/logo/innohub_logo.png"
              alt="InnoHub Logo"
              width={40}
              height={40}
              className="object-cover"
            />
          </div>
          <span className="font-bold text-xl text-white">INNO<span className="text-purple-500">HUB</span></span>
        </Link>

        {/* Desktop Navigation (Center) */}
        <nav className="hidden md:flex items-center absolute left-1/2 transform -translate-x-1/2">
          <div className="flex items-center">
            <HoverBorderGradient
              containerClassName="rounded-full"
              className="dark:bg-slate-950 bg-slate-950 text-white flex items-center space-x-1 px-2 py-1.5"
              duration={2}
            >
              <div className="inline-flex items-center">
                {navLinks.map((link) => (
                  <Link
                    key={link.href}
                    href={link.href}
                    className={`text-white/80 hover:text-white transition-colors text-sm font-medium px-4 py-1.5 rounded-full ${
                      pathname === link.href ? 'text-white border-b-2 border-purple-500' : ''
                    }`}
                  >
                    {isLoaded ? t(link.key) : ''}
                  </Link>
                ))}
              </div>
            </HoverBorderGradient>
          </div>
        </nav>

        {/* Right Side Actions */}
        <div className="hidden md:flex items-center gap-4 z-10">
          {/* Contact Us Button */}
          <Link href="/contact">
            <HoverBorderGradient
              containerClassName="rounded-full"
              className="dark:bg-slate-950 bg-slate-950 text-white px-4 py-1.5 text-sm"
              duration={2}
            >
              {isLoaded ? t('nav.contact') : ''}
            </HoverBorderGradient>
          </Link>

          {/* Language Switcher */}
          {isLoaded && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <HoverBorderGradient
                  containerClassName="rounded-full h-[2.25rem] w-[3rem]"
                  className="dark:bg-slate-950 bg-slate-950 text-white flex items-center justify-center gap-1 h-full w-full px-2"
                  duration={2}
                >
                  <Globe className="h-4 w-4" />
                  <span className="text-sm">{language === 'en' ? '🇺🇸' : '🇲🇳'}</span>
                  <span className="sr-only">{t('nav.language')}</span>
                </HoverBorderGradient>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="bg-black/90 backdrop-blur-md border border-purple-500 rounded-xl">
                <DropdownMenuItem
                  className={`${language === 'en' ? 'bg-purple-700/30' : ''} text-white hover:bg-black/70 rounded-lg my-1 flex items-center gap-2`}
                  onClick={() => setLanguage('en')}
                >
                  <span className="text-lg">🇺🇸</span>
                  {t('nav.english')}
                </DropdownMenuItem>
                <DropdownMenuItem
                  className={`${language === 'mn' ? 'bg-purple-700/30' : ''} text-white hover:bg-black/70 rounded-lg my-1 flex items-center gap-2`}
                  onClick={() => setLanguage('mn')}
                >
                  <span className="text-lg">🇲🇳</span>
                  {t('nav.mongolian')}
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          )}

          {/* Login Button - Removed */}
        </div>

        {/* Mobile Navigation */}
        <Sheet>
          <SheetTrigger asChild className="md:hidden">
            <HoverBorderGradient
              containerClassName="rounded-full h-[2.75rem] md:hidden"
              className="dark:bg-slate-950 bg-slate-950 text-white flex items-center justify-center h-full px-3 py-1.5"
              duration={2}
            >
              <Menu className="h-6 w-6" />
              <span className="sr-only">Toggle menu</span>
            </HoverBorderGradient>
          </SheetTrigger>
          <SheetContent side="right" className="w-[300px] sm:w-[400px] bg-black/95 border-purple-500">
            <nav className="flex flex-col gap-4 mt-8">
              <HoverBorderGradient
                containerClassName="rounded-2xl"
                className="dark:bg-slate-950 bg-slate-950 text-white flex flex-col w-full py-2 px-2"
                duration={2}
              >
                <div className="flex flex-col w-full">
                  {navLinks.map((link) => (
                    <Link
                      key={link.href}
                      href={link.href}
                      className={`text-white/80 hover:text-white transition-colors py-3 px-4 text-base font-medium rounded-xl ${
                        pathname === link.href ? 'text-white border-l-2 border-purple-500' : ''
                      }`}
                    >
                      {isLoaded ? t(link.key) : ''}
                    </Link>
                  ))}
                  <Link
                    href="/contact"
                    className="text-white/80 hover:text-white transition-colors py-3 px-4 text-base font-medium rounded-xl"
                  >
                    {isLoaded ? t('nav.contact') : ''}
                  </Link>
                </div>
              </HoverBorderGradient>
              {isLoaded && (
                <div className="mt-4 flex flex-col gap-4">
                  {/* Language Switcher for Mobile */}
                  <div className="flex flex-col gap-2">
                    <p className="text-white/60 text-sm">{t('nav.language')}:</p>
                    <div className="flex gap-2">
                      <HoverBorderGradient
                        containerClassName="rounded-full h-[2.25rem]"
                        className={`dark:bg-slate-950 ${language === 'en' ? 'bg-purple-800' : 'bg-slate-950'} text-white flex items-center justify-center gap-2 px-4 py-1.5 text-sm`}
                        onClick={() => setLanguage('en')}
                        duration={2}
                      >
                        <span className="text-sm">🇺🇸</span>
                        {t('nav.english')}
                      </HoverBorderGradient>
                      <HoverBorderGradient
                        containerClassName="rounded-full h-[2.25rem]"
                        className={`dark:bg-slate-950 ${language === 'mn' ? 'bg-purple-800' : 'bg-slate-950'} text-white flex items-center justify-center gap-2 px-4 py-1.5 text-sm`}
                        onClick={() => setLanguage('mn')}
                        duration={2}
                      >
                        <span className="text-sm">🇲🇳</span>
                        {t('nav.mongolian')}
                      </HoverBorderGradient>
                    </div>
                  </div>
                  {/* Login button removed */}
                </div>
              )}
            </nav>
          </SheetContent>
        </Sheet>
      </div>
    </header>
  );
}
