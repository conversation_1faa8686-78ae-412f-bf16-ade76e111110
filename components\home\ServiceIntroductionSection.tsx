"use client";
import React from 'react';
import ServiceCard from './ServiceCard'; // Import the new ServiceCard component
import { motion } from 'framer-motion';
import TimelineDemo from '@/components/ui/timeline-demo';

interface ServiceDataItem {
  title: string;
  description: string;
  href: string;
  imageUrl: string; // Added imageUrl
  delay: number;
}

const servicesData: ServiceDataItem[] = [
  {
    title: "Business Planning",
    description: "Develop a comprehensive business plan with market analysis, financial projections, and growth strategies. Our experts will guide you through the process of creating a roadmap for your startup's success.",
    href: "/services/business-planning",
    imageUrl: "/images/Background/background-of-4.png",
    delay: 0,
  },
  {
    title: "Mentorship & Networking",
    description: "Connect with experienced entrepreneurs, industry experts, and potential partners through our extensive network. Receive personalized mentorship to navigate challenges and seize opportunities.",
    href: "/services/mentorship",
    imageUrl: "/images/Background/background-of-4.png",
    delay: 0.1,
  },
  {
    title: "Funding Access",
    description: "Get support in preparing for investment rounds, refining your pitch, and connecting with potential investors. We help startups access grants, angel investment, venture capital, and other funding sources.",
    href: "/services/funding",
    imageUrl: "/images/Background/background-of-4.png",
    delay: 0.2,
  },
  {
    title: "Workspace & Resources",
    description: "Access modern coworking spaces, meeting rooms, and essential business resources. Our incubator provides the infrastructure you need to focus on growing your business in a collaborative environment.",
    href: "/services/workspace",
    imageUrl: "/images/Background/background-of-4.png",
    delay: 0.3,
  },
];

const ServiceIntroductionSection = () => {
  return (
    <>
      {/* Services Section - Background removed as requested */}
      <section
        className="py-20 text-white relative bg-black" // Background image removed, using solid black background
      >

        <div className="container mx-auto px-6 relative"> {/* z-10 removed as there's no overlay anymore */}
          {/* Title from the image: "Expertise in Data Science and Artificial Intelligence" */}
          <motion.h2
            className="text-3xl md:text-4xl font-semibold text-center mb-4 text-gray-300"
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, ease: "easeOut" }}
          >
            Comprehensive Support for Entrepreneurs
          </motion.h2>
          {/* The "Our Core Services" title might be redundant if the above title is used,
              or could be smaller if kept. For now, I'll comment it out to match the image more closely.
          <motion.h2
            className="text-4xl md:text-5xl font-bold text-center mb-16"
            initial={{ opacity: 0, y: -30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7, ease: "easeOut" }}
          >
            Our Core Services
          </motion.h2>
          */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-10 md:gap-12 mt-12 will-change-transform"> {/* Added will-change-transform for better performance */}
            {servicesData.map((service, index) => (
              <div key={`service-container-${service.title}`} className="will-change-transform">
                <ServiceCard
                  key={`service-${service.title}`}
                  title={service.title}
                  description={service.description}
                  href={service.href}
                  imageUrl={service.imageUrl}
                  delay={index * 0.1} // More consistent delay calculation
                />
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Timeline Component - Separate section with no background */}
      <section className="bg-black text-white py-20">
        <TimelineDemo />
      </section>
    </>
  );
};

export default ServiceIntroductionSection;
