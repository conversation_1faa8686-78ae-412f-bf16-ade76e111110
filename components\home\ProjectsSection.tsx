'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import { <PERSON>, CardContent, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { TextReveal } from '@/components/ui/aceternity/text-reveal';

const projects = [
  {
    id: 1,
    title: 'EcoGrow',
    category: 'AgriTech',
    technologies: ['IoT', 'Sustainable Farming'],
    image: '/placeholder-1.jpg',
    link: '/investors/ecogrow',
  },
  {
    id: 2,
    title: 'FinEdge',
    category: 'FinTech',
    technologies: ['Digital Banking', 'Blockchain'],
    image: '/placeholder-2.jpg',
    link: '/investors/finedge',
  },
  {
    id: 3,
    title: 'MediConnect',
    category: 'HealthTech',
    technologies: ['Telemedicine', 'AI Diagnostics'],
    image: '/placeholder-3.jpg',
    link: '/investors/mediconnect',
  },
  {
    id: 4,
    title: 'SmartRetail',
    category: 'RetailTech',
    technologies: ['Computer Vision', 'Inventory Management'],
    image: '/placeholder-4.jpg',
    link: '/investors/smartretail',
  },
];

export default function ProjectsSection() {
  return (
    <section className="py-20 bg-muted/30">
      <div className="container mx-auto px-4">
        <TextReveal className="text-center max-w-3xl mx-auto mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Success Stories
          </h2>
          <p className="text-muted-foreground text-lg">
            Discover some of the innovative startups that have thrived with our incubator support.
          </p>
        </TextReveal>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mt-12">
          {projects.map((project, index) => (
            <motion.div
              key={project.id}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              viewport={{ once: true }}
              whileHover={{ y: -5 }}
              className="h-full"
            >
              <Link href={project.link} className="h-full block">
                <Card className="overflow-hidden h-full flex flex-col border-border/50 bg-background/50 backdrop-blur-sm transition-all duration-300 hover:border-primary/50 hover:shadow-lg">
                  <div className="relative aspect-[4/3] bg-muted">
                    <div className="absolute inset-0 flex items-center justify-center text-muted-foreground">
                      {project.id.toString().padStart(3, '0')}
                    </div>
                  </div>
                  <CardContent className="flex-grow pt-6">
                    <h3 className="font-semibold text-xl mb-2">{project.title}</h3>
                    <p className="text-muted-foreground text-sm">{project.category}</p>
                  </CardContent>
                  <CardFooter className="flex flex-wrap gap-2 pt-0 pb-6">
                    {project.technologies.map((tech) => (
                      <span
                        key={tech}
                        className="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent bg-primary/10 text-primary hover:bg-primary/20"
                      >
                        {tech}
                      </span>
                    ))}
                  </CardFooter>
                </Card>
              </Link>
            </motion.div>
          ))}
        </div>

        <div className="text-center mt-16">
          <Button size="lg" asChild>
            <Link href="/success-stories">View All Success Stories</Link>
          </Button>
        </div>
      </div>
    </section>
  );
}
