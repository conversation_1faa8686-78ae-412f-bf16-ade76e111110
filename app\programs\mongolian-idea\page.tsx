'use client';

import React from 'react';
import { motion } from 'framer-motion';
import Link from 'next/link';
import { BokehBackground } from '@/components/ui/aceternity/bokeh-background';
import { ArrowLeft } from 'lucide-react';

export default function MongolianIdeaProgramPage() {
  return (
    <>
      {/* Hero Section */}
      <section className="relative h-[60vh] overflow-hidden bg-black">
        {/* Background Image */}
        <div className="absolute inset-0">
          <div
            className="absolute inset-0 bg-cover bg-center opacity-70"
            style={{
              backgroundImage: 'url(/images/programs/2.jpg)',
              backgroundPosition: 'center center'
            }}
          />
          <div className="absolute inset-0 bg-gradient-to-b from-black/70 via-black/50 to-black" />
        </div>

        {/* Content */}
        <div className="relative z-10 h-full flex flex-col items-center justify-center text-center px-4">
          <Link 
            href="/programs" 
            className="absolute top-8 left-8 text-white/80 hover:text-white flex items-center transition-colors"
          >
            <ArrowLeft className="mr-2 h-5 w-5" />
            Буцах
          </Link>
          
          <motion.h1
            className="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-6 max-w-4xl"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            МОНГОЛ ОЮУН САНАА
          </motion.h1>

          <motion.div
            className="w-24 h-1 bg-primary mb-8"
            initial={{ width: 0 }}
            animate={{ width: 96 }}
            transition={{ duration: 1, delay: 0.5 }}
          />

          <motion.p
            className="text-xl text-white/80 max-w-2xl mb-8"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.3 }}
          >
            Ирээдүйн үнэ цэнээ өсгөх, асуудал шийдвэрлэх, инновацийг таслиж гаргах ирэх, оюутан залуусын бизнес сэтгэлгээ сайжруулан ирээдүйн шилдэг ЭНТРЕПРЕНЕРүүдийг бий болгох 3 сарын хөтөлбөр.
          </motion.p>
        </div>
      </section>

      {/* Program Details Section */}
      <BokehBackground
        className="py-24"
        colors={['#ffcc33', '#ffaa00', '#ff8800', '#ffffff']}
        density={30}
      >
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <motion.div
              className="bg-black/30 backdrop-blur-md rounded-xl border border-white/10 p-8 mb-12"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
            >
              <h2 className="text-2xl md:text-3xl font-bold text-white mb-6">Хөтөлбөрийн тухай</h2>
              <p className="text-white/80 mb-6">
                Монгол Оюун Санаа хөтөлбөр нь оюутан залуусын бизнес сэтгэлгээг хөгжүүлэх, инновацийг дэмжих, асуудал шийдвэрлэх чадварыг сайжруулах зорилготой 3 сарын хөтөлбөр юм. Энэхүү хөтөлбөр нь оюутан залуусыг ирээдүйн шилдэг бизнес эрхлэгчид болгон бэлтгэхэд чиглэгдсэн.
              </p>
              <p className="text-white/80 mb-6">
                Хөтөлбөрт хамрагдсанаар та дараах боломжуудыг авна:
              </p>
              <ul className="list-disc pl-6 text-white/80 space-y-2 mb-6">
                <li>Бизнес сэтгэлгээгээ хөгжүүлэх</li>
                <li>Асуудал шийдвэрлэх чадвараа сайжруулах</li>
                <li>Инновацийг бий болгох арга замуудыг судлах</li>
                <li>Бизнес загвар боловсруулах</li>
                <li>Багаар ажиллах чадвараа сайжруулах</li>
                <li>Төслөө танилцуулах, илтгэх чадвараа хөгжүүлэх</li>
              </ul>
              <p className="text-white/80">
                Хөтөлбөрт хамрагдахын тулд та дараах шаардлагуудыг хангасан байх шаардлагатай:
              </p>
              <ul className="list-disc pl-6 text-white/80 space-y-2 mt-4">
                <li>18-35 насны залуучууд</li>
                <li>Бизнес эрхлэх, инновацийг бий болгох сонирхолтой</li>
                <li>Хөтөлбөрийн хугацаанд идэвхтэй оролцох боломжтой</li>
              </ul>
            </motion.div>

            <motion.div
              className="text-center"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.8, delay: 0.5 }}
            >
              <h2 className="text-2xl md:text-3xl font-bold text-white mb-6">Хөтөлбөрт бүртгүүлэх</h2>
              <p className="text-white/80 mb-8 max-w-2xl mx-auto">
                Хөтөлбөрт бүртгүүлэхийн тулд доорх товчийг дарж өргөдлөө бөглөнө үү. Бүртгэл 2024 оны 7-р сарын 15-нд хаагдана.
              </p>
              <motion.button
                className="px-8 py-3 bg-primary text-white rounded-full font-medium hover:bg-primary/90 transition-colors duration-300 shadow-lg shadow-primary/20"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.98 }}
              >
                Бүртгүүлэх
              </motion.button>
            </motion.div>
          </div>
        </div>
      </BokehBackground>
    </>
  );
}
