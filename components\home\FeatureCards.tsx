'use client';

import React from 'react';
import { motion } from 'framer-motion';
export default function FeatureCards() {
  return (
    <div className="relative py-20 bg-black text-white text-center overflow-hidden"> {/* Added relative and overflow-hidden */}
      {/* Left curved line */}
      <div className="absolute top-1/2 left-0 transform -translate-y-1/2 w-24 h-24 border-l border-t border-gray-700 rounded-tl-3xl opacity-50"></div>
      {/* Right curved line */}
      <div className="absolute top-1/2 right-0 transform -translate-y-1/2 w-24 h-24 border-r border-b border-gray-700 rounded-br-3xl opacity-50"></div>

      <div className="relative z-10 max-w-3xl mx-auto px-4 sm:px-6 lg:px-8"> {/* Added relative z-10 and adjusted max-w */}
        <motion.h2
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true }}
          className="text-3xl md:text-4xl font-bold mb-4 text-purple-500"
        >
          Entrepreneurial Success
        </motion.h2>
        <motion.p
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          viewport={{ once: true }}
          className="text-xl text-gray-300"
        >
          We provide the resources, expertise, and environment needed to transform innovative ideas into thriving businesses. Our incubator is designed to accelerate your entrepreneurial journey.
        </motion.p>
      </div>
    </div>
  );
}
