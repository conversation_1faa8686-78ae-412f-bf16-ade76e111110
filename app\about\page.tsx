'use client';

import { useState, useEffect, useRef } from 'react';
import { CursorImageEffect } from '@/components/ui/aceternity/cursor-image-effect';
import { useLanguage } from '@/lib/context/language-context';
import Image from 'next/image';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';

export default function AboutPage() {
  const { t, language } = useLanguage();
  const [activeSection, setActiveSection] = useState('whatWeDo');

  // Refs for each section
  const whatWeDoRef = useRef<HTMLDivElement>(null);
  const ourTeamRef = useRef<HTMLDivElement>(null);
  const mentorsRef = useRef<HTMLDivElement>(null);

  // Investor images for cursor effect
  const investorImages = [
    '/images/investors/1.png',
    '/images/investors/2.png',
    '/images/investors/3.png',
    '/images/investors/4.png',
    '/images/investors/5.png',
  ];

  // Team members data
  const teamMembers = [
    {
      name: t('about.teamMember1'),
      role: t('about.teamMember1Role'),
      image: '/images/investors/1.png', // Using placeholder image
    },
    {
      name: t('about.teamMember2'),
      role: t('about.teamMember2Role'),
      image: '/images/investors/2.png', // Using placeholder image
    },
  ];

  // Mentors data
  const mentors = [
    {
      name: t('about.mentor1'),
      role: t('about.mentor1Role'),
      image: '/images/investors/3.png', // Using placeholder image
    },
    {
      name: t('about.mentor2'),
      role: t('about.mentor2Role'),
      image: '/images/investors/4.png', // Using placeholder image
    },
  ];

  // What we do paragraphs
  const whatWeDoItems = [
    t('about.whatWeDoItem1'),
    t('about.whatWeDoItem2'),
    t('about.whatWeDoItem3'),
    t('about.whatWeDoItem4'),
    t('about.whatWeDoItem5'),
  ];

  // Navigation items
  const navItems = [
    { id: 'whatWeDo', label: t('about.whatWeDo'), ref: whatWeDoRef },
    { id: 'ourTeam', label: t('about.ourTeam'), ref: ourTeamRef },
    { id: 'mentors', label: t('about.mentors'), ref: mentorsRef },
  ];

  // Scroll to section when clicking on nav item
  const scrollToSection = (id: string) => {
    const sectionRef = navItems.find(item => item.id === id)?.ref;
    if (sectionRef?.current) {
      sectionRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  };

  // Update active section based on scroll position
  useEffect(() => {
    const handleScroll = () => {
      const scrollPosition = window.scrollY + 200; // Adding offset for better UX

      // Check which section is in view
      if (whatWeDoRef.current && scrollPosition >= whatWeDoRef.current.offsetTop &&
          ourTeamRef.current && scrollPosition < ourTeamRef.current.offsetTop) {
        setActiveSection('whatWeDo');
      } else if (ourTeamRef.current && scrollPosition >= ourTeamRef.current.offsetTop &&
                mentorsRef.current && scrollPosition < mentorsRef.current.offsetTop) {
        setActiveSection('ourTeam');
      } else if (mentorsRef.current && scrollPosition >= mentorsRef.current.offsetTop) {
        setActiveSection('mentors');
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <>
      {/* First page with cursor effect */}
      <CursorImageEffect
        className="min-h-[90vh] flex flex-col items-center justify-center pt-32 pb-20 bg-black relative"
        images={investorImages}
        imageSize={300}
        imageOpacity={0.8} // Slightly transparent
      >
        {/* Empty container - only cursor effect */}
        {/* You can add content here later */}
      </CursorImageEffect>

      {/* Content sections */}
      <section className="bg-black py-20">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row gap-8">
            {/* Left sidebar navigation */}
            <div className="md:w-1/4">
              <div className="sticky top-32 space-y-4">
                {navItems.map((item) => (
                  <button
                    key={item.id}
                    onClick={() => scrollToSection(item.id)}
                    className={cn(
                      "text-left w-full py-2 px-4 rounded-lg transition-all",
                      activeSection === item.id
                        ? "bg-primary/20 text-white font-medium border-l-4 border-primary"
                        : "text-white/70 hover:text-white hover:bg-white/5"
                    )}
                  >
                    {item.label}
                  </button>
                ))}
              </div>
            </div>

            {/* Right content area */}
            <div className="md:w-3/4 space-y-32"> {/* Added space between sections */}
              {/* What We Do Section */}
              <motion.div
                ref={whatWeDoRef}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
                className="space-y-8 pt-4"
                id="whatWeDo"
              >
                <h2 className="text-3xl font-bold text-white">
                  {t('about.whatWeDoTitle')}
                </h2>
                <div className="space-y-6">
                  {whatWeDoItems.map((item, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, y: 20 }}
                      whileInView={{ opacity: 1, y: 0 }}
                      viewport={{ once: true }}
                      transition={{ duration: 0.5, delay: index * 0.1 }}
                      className="bg-black/30 backdrop-blur-md border border-primary/10 rounded-xl p-6"
                    >
                      <p className="text-white/90">{item}</p>
                    </motion.div>
                  ))}
                </div>
              </motion.div>

              {/* Our Team Section */}
              <motion.div
                ref={ourTeamRef}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5 }}
                className="space-y-8 pt-4"
                id="ourTeam"
              >
                <h2 className="text-3xl font-bold text-white">
                  {t('about.ourTeamTitle')}
                </h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {teamMembers.map((member, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, y: 20 }}
                      whileInView={{ opacity: 1, y: 0 }}
                      viewport={{ once: true }}
                      transition={{ duration: 0.5, delay: index * 0.1 }}
                      className="bg-black/30 backdrop-blur-md border border-primary/10 rounded-xl p-6 flex flex-col items-center"
                    >
                      <div className="relative w-32 h-32 mb-4">
                        <Image
                          src={member.image}
                          alt={member.name}
                          fill
                          className="object-cover rounded-lg"
                        />
                      </div>
                      <h3 className="text-xl font-semibold text-white">{member.name}</h3>
                      <p className="text-white/70 text-center mt-2">{member.role}</p>
                    </motion.div>
                  ))}
                </div>
              </motion.div>

              {/* Mentors Section */}
              <motion.div
                ref={mentorsRef}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5 }}
                className="space-y-8 pt-4"
                id="mentors"
              >
                <h2 className="text-3xl font-bold text-white">
                  {t('about.mentorsTitle')}
                </h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {mentors.map((mentor, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, y: 20 }}
                      whileInView={{ opacity: 1, y: 0 }}
                      viewport={{ once: true }}
                      transition={{ duration: 0.5, delay: index * 0.1 }}
                      className="bg-black/30 backdrop-blur-md border border-primary/10 rounded-xl p-6 flex flex-col items-center"
                    >
                      <div className="relative w-32 h-32 mb-4">
                        <Image
                          src={mentor.image}
                          alt={mentor.name}
                          fill
                          className="object-cover rounded-lg"
                        />
                      </div>
                      <h3 className="text-xl font-semibold text-white">{mentor.name}</h3>
                      <p className="text-white/70 text-center mt-2">{mentor.role}</p>
                    </motion.div>
                  ))}
                </div>
              </motion.div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
}
