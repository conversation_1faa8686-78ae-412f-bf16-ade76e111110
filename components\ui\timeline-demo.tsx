"use client";

import React, { useRef } from "react";
import { motion, useScroll, useTransform } from "framer-motion";

const TimelineImage = ({
  src,
  alt,
  caption,
  title,
  description,
  index
}: {
  src: string;
  alt: string;
  caption: string;
  title: string;
  description: string;
  index: number;
}) => {
  const containerRef = useRef<HTMLDivElement>(null);

  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start end", "end start"]
  });

  // Create different transform values based on index to make each image unique
  const rotateY = useTransform(
    scrollYProgress,
    [0, 0.5, 1],
    index % 2 === 0 ? [-20, 0, 20] : [20, 0, -20]
  );

  const rotateX = useTransform(
    scrollYProgress,
    [0, 0.5, 1],
    index % 2 === 0 ? [15, 0, -15] : [-15, 0, 15]
  );

  const rotate = useTransform(
    scrollYProgress,
    [0, 0.5, 1],
    index % 2 === 0 ? [-10, 0, 10] : [10, 0, -10]
  );

  const y = useTransform(
    scrollYProgress,
    [0, 0.5, 1],
    ["-20%", "0%", "20%"]
  );

  const opacity = useTransform(
    scrollYProgress,
    [0, 0.3, 0.7, 1],
    [0.6, 1, 1, 0.6]
  );

  const scale = useTransform(
    scrollYProgress,
    [0, 0.5, 1],
    [0.9, 1, 0.9]
  );

  return (
    <div className="timeline-item">
      <div className="timeline-image-side">
        <div ref={containerRef} className="timeline-image-container">
          <motion.div
            className="timeline-image-wrapper"
            style={{
              rotateY,
              rotateX,
              rotate,
              y,
              opacity,
              scale,
              x: index % 2 === 0 ? "-5%" : "5%",
              backgroundColor: "#111"
            }}
          >
            <div className="timeline-image-blur">
              <img
                src={src}
                alt={alt}
                width={1200}
                height={800}
                className="w-full h-full object-contain"
              />
            </div>
            <div className="timeline-image-main">
              <img
                src={src}
                alt={alt}
                width={1200}
                height={800}
                className="w-full h-full object-contain"
              />
            </div>
          </motion.div>
        </div>
      </div>

      <div className="timeline-content-side">
        <h3 className="timeline-title">{title}</h3>
        <p className="timeline-image-caption">{caption}</p>
        <p className="timeline-description">{description}</p>
        <div className="flex space-x-4 mt-6">
          {caption.includes("Google Cloud") && (
            <button className="bg-neutral-800 text-white text-sm px-4 py-2 rounded-full hover:bg-neutral-700 transition-colors">
              Google Cloud
            </button>
          )}
          {caption.includes("Google Gemini") && (
            <button className="bg-neutral-800 text-white text-sm px-4 py-2 rounded-full hover:bg-neutral-700 transition-colors">
              Google Gemini
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default function TimelineDemo() {
  const timelineItems = [
    {
      id: "faq-bot",
      title: "FAQ Bot",
      src: "/images/Timeline/1.png",
      alt: "FAQ Bot showcase",
      caption: "Google Cloud | Google Gemini",
      description: "Our intelligent FAQ Bot leverages advanced AI to provide instant answers to customer inquiries, improving response times and satisfaction rates."
    },
    {
      id: "data-analytics",
      title: "Data Analytics",
      src: "/images/Timeline/2.png",
      alt: "Data Analytics platform",
      caption: "Cutting-edge technology integration",
      description: "Transform your business with our powerful data analytics solutions that turn complex information into actionable insights."
    },
    {
      id: "workflow-automation",
      title: "Workflow Automation",
      src: "/images/Timeline/3.png",
      alt: "Workflow Automation system",
      caption: "Seamless process integration",
      description: "Streamline operations and boost productivity with our custom workflow automation systems designed for your specific business needs."
    }
  ];

  return (
    <section className="timeline-section py-20">
      <h2 className="text-4xl font-bold text-center mb-16 text-white">Our Solutions</h2>

      {timelineItems.map((item, index) => (
        <TimelineImage
          key={`timeline-item-${item.id}`}
          src={item.src}
          alt={item.alt}
          title={item.title}
          caption={item.caption}
          description={item.description}
          index={index}
        />
      ))}
    </section>
  );
}
