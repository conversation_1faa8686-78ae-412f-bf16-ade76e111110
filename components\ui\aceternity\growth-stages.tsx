'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';

export const GrowthStages = ({
  className,
  stages = 5,
  activeStage = 3,
}: {
  className?: string;
  stages?: number;
  activeStage?: number;
}) => {
  return (
    <div className={cn('flex justify-center items-end gap-4 md:gap-8 py-10', className)}>
      {Array.from({ length: stages }).map((_, i) => {
        const isActive = i < activeStage;
        const height = 40 + (i * 20);
        
        return (
          <motion.div
            key={i}
            className="relative flex flex-col items-center"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: i * 0.2, duration: 0.5 }}
          >
            <div className="w-full flex justify-center">
              <motion.div 
                className={cn(
                  "w-[2px] rounded-full",
                  isActive ? "bg-primary" : "bg-muted"
                )}
                style={{ height: `${height}px` }}
                initial={{ height: 0 }}
                animate={{ height: `${height}px` }}
                transition={{ duration: 1, delay: i * 0.3 }}
              />
            </div>
            
            <motion.div
              className={cn(
                "w-16 h-16 rounded-full flex items-center justify-center",
                isActive ? "bg-primary/20" : "bg-muted/20"
              )}
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ duration: 0.5, delay: i * 0.3 + 0.5 }}
            >
              <motion.div
                className={cn(
                  "w-10 h-10 rounded-full flex items-center justify-center text-sm font-bold",
                  isActive ? "bg-primary text-primary-foreground" : "bg-muted text-muted-foreground"
                )}
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ duration: 0.3, delay: i * 0.3 + 0.7 }}
              >
                {i + 1}
              </motion.div>
            </motion.div>
            
            <motion.div
              className="mt-2 text-center text-xs font-medium"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5, delay: i * 0.3 + 0.9 }}
            >
              {i === 0 && "Seed"}
              {i === 1 && "Sprout"}
              {i === 2 && "Growth"}
              {i === 3 && "Bloom"}
              {i === 4 && "Harvest"}
            </motion.div>
          </motion.div>
        );
      })}
    </div>
  );
};
