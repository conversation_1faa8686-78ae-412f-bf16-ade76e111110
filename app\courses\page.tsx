'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useLanguage } from '@/lib/context/language-context';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent } from '@/components/ui/card';
import { Search, BookOpen, PlayCircle, Clock, Award, Filter } from 'lucide-react';

// Mock data for courses
const COURSES_DATA = [
  {
    id: 1,
    title: 'Introduction to Entrepreneurship',
    description: 'Learn the fundamentals of starting and growing a successful business',
    thumbnail: '/images/courses/entrepreneurship.jpg',
    duration: '1h 45m',
    lessons: 12,
    level: 'Beginner',
    instructor: '<PERSON>',
    category: 'Business',
    videoId: 'Dn-Yk5YRMD8',
  },
  {
    id: 2,
    title: 'Startup Funding Strategies',
    description: 'Discover various funding options for your startup and how to secure investments',
    thumbnail: '/images/courses/funding.jpg',
    duration: '2h 30m',
    lessons: 15,
    level: 'Intermediate',
    instructor: '<PERSON>',
    category: 'Finance',
    videoId: 'Dn-Yk5YRMD8',
  },
  {
    id: 3,
    title: 'Product Development Workshop',
    description: 'A comprehensive guide to developing and launching successful products',
    thumbnail: '/images/courses/product.jpg',
    duration: '3h 15m',
    lessons: 20,
    level: 'Advanced',
    instructor: 'Michael Chen',
    category: 'Product',
    videoId: 'Dn-Yk5YRMD8',
  },
  {
    id: 4,
    title: 'Marketing for Startups',
    description: 'Effective marketing strategies for new businesses with limited budgets',
    thumbnail: '/images/courses/marketing.jpg',
    duration: '2h 10m',
    lessons: 14,
    level: 'Beginner',
    instructor: 'Emma Davis',
    category: 'Marketing',
    videoId: 'Dn-Yk5YRMD8',
  },
  {
    id: 5,
    title: 'Business Model Innovation',
    description: 'Learn how to create and iterate on innovative business models',
    thumbnail: '/images/courses/business-model.jpg',
    duration: '1h 55m',
    lessons: 10,
    level: 'Intermediate',
    instructor: 'David Wilson',
    category: 'Business',
    videoId: 'Dn-Yk5YRMD8',
  },
  {
    id: 6,
    title: 'Pitching to Investors',
    description: 'Master the art of creating and delivering compelling investor pitches',
    thumbnail: '/images/courses/pitching.jpg',
    duration: '1h 30m',
    lessons: 8,
    level: 'Intermediate',
    instructor: 'Lisa Brown',
    category: 'Finance',
    videoId: 'Dn-Yk5YRMD8',
  },
];

// Course card component
const CourseCard = ({ course }: { course: typeof COURSES_DATA[0] }) => {
  const { t, isLoaded } = useLanguage();
  
  return (
    <Link href={`/courses/${course.id}`}>
      <Card className="overflow-hidden border-primary/10 hover:border-primary/30 transition-all hover:shadow-md bg-black/60">
        <div className="relative aspect-video overflow-hidden">
          <div className="absolute inset-0 bg-black/20 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity z-10">
            <PlayCircle className="h-12 w-12 text-white" />
          </div>
          <div className="absolute bottom-2 right-2 bg-black/70 text-white text-xs px-2 py-1 rounded z-10">
            {course.duration}
          </div>
          <Image
            src={course.thumbnail}
            alt={course.title}
            fill
            className="object-cover"
            onError={(e) => {
              // Fallback for missing images
              const target = e.target as HTMLImageElement;
              target.src = 'https://placehold.co/600x400/3a0647/ffffff?text=InnoHub+Course';
            }}
          />
        </div>
        <CardContent className="p-4">
          <h3 className="font-semibold text-lg line-clamp-1">{course.title}</h3>
          <p className="text-muted-foreground text-sm mt-1 line-clamp-2">{course.description}</p>
          <div className="flex items-center mt-3 text-xs text-muted-foreground">
            <BookOpen className="h-3 w-3 mr-1" />
            <span>{course.lessons} {isLoaded ? t('courses.lessons') : 'lessons'}</span>
            <span className="mx-2">•</span>
            <Clock className="h-3 w-3 mr-1" />
            <span>{course.duration}</span>
            <span className="mx-2">•</span>
            <Award className="h-3 w-3 mr-1" />
            <span>{course.level}</span>
          </div>
        </CardContent>
      </Card>
    </Link>
  );
};

export default function CoursesPage() {
  const { t, isLoaded } = useLanguage();
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredCourses, setFilteredCourses] = useState(COURSES_DATA);
  const [activeCategory, setActiveCategory] = useState('all');
  
  // Filter courses based on search query and category
  useEffect(() => {
    let result = COURSES_DATA;
    
    if (searchQuery) {
      result = result.filter(course => 
        course.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        course.description.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }
    
    if (activeCategory !== 'all') {
      result = result.filter(course => course.category.toLowerCase() === activeCategory.toLowerCase());
    }
    
    setFilteredCourses(result);
  }, [searchQuery, activeCategory]);
  
  return (
    <div className="min-h-screen bg-black pt-24 pb-16">
      <div className="container mx-auto px-4">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4">
          <div>
            <h1 className="text-3xl font-bold">{isLoaded ? t('courses.title') : 'InnoHub Courses'}</h1>
            <p className="text-muted-foreground mt-1">
              {isLoaded ? t('courses.subtitle') : 'Accelerate your entrepreneurial journey with our expert-led courses'}
            </p>
          </div>
          <div className="relative w-full md:w-64">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input
              placeholder={isLoaded ? t('courses.searchPlaceholder') : 'Search courses...'}
              className="pl-10"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </div>
        
        <Tabs defaultValue="all" className="mb-8">
          <div className="flex items-center justify-between mb-4">
            <TabsList className="bg-background/5">
              <TabsTrigger 
                value="all" 
                onClick={() => setActiveCategory('all')}
                className="data-[state=active]:bg-primary/20"
              >
                {isLoaded ? t('courses.allCourses') : 'All Courses'}
              </TabsTrigger>
              <TabsTrigger 
                value="business" 
                onClick={() => setActiveCategory('business')}
                className="data-[state=active]:bg-primary/20"
              >
                {isLoaded ? t('courses.business') : 'Business'}
              </TabsTrigger>
              <TabsTrigger 
                value="finance" 
                onClick={() => setActiveCategory('finance')}
                className="data-[state=active]:bg-primary/20"
              >
                {isLoaded ? t('courses.finance') : 'Finance'}
              </TabsTrigger>
              <TabsTrigger 
                value="marketing" 
                onClick={() => setActiveCategory('marketing')}
                className="data-[state=active]:bg-primary/20"
              >
                {isLoaded ? t('courses.marketing') : 'Marketing'}
              </TabsTrigger>
              <TabsTrigger 
                value="product" 
                onClick={() => setActiveCategory('product')}
                className="data-[state=active]:bg-primary/20"
              >
                {isLoaded ? t('courses.product') : 'Product'}
              </TabsTrigger>
            </TabsList>
            
            <Button variant="outline" size="sm" className="hidden md:flex items-center gap-2 border-primary/20">
              <Filter className="h-4 w-4" />
              {isLoaded ? t('courses.filter') : 'Filter'}
            </Button>
          </div>
          
          <TabsContent value="all" className="mt-0">
            {filteredCourses.length > 0 ? (
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredCourses.map(course => (
                  <CourseCard key={course.id} course={course} />
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <div className="bg-primary/10 rounded-full p-4 inline-flex mb-4">
                  <Search className="h-6 w-6 text-primary" />
                </div>
                <h3 className="text-xl font-semibold">
                  {isLoaded ? t('courses.noResults') : 'No courses found'}
                </h3>
                <p className="text-muted-foreground mt-2">
                  {isLoaded ? t('courses.tryDifferent') : 'Try a different search term or browse all courses'}
                </p>
              </div>
            )}
          </TabsContent>
          
          {/* The other tab contents will be handled by the useEffect filter */}
          <TabsContent value="business" className="mt-0"></TabsContent>
          <TabsContent value="finance" className="mt-0"></TabsContent>
          <TabsContent value="marketing" className="mt-0"></TabsContent>
          <TabsContent value="product" className="mt-0"></TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
